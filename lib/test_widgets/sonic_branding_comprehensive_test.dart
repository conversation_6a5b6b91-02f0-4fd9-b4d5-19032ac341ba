import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:sso_futurescape/services/sonic_branding_service.dart';

/// Comprehensive test widget for sonic branding functionality
class SonicBrandingComprehensiveTest extends StatefulWidget {
  @override
  _SonicBrandingComprehensiveTestState createState() => _SonicBrandingComprehensiveTestState();
}

class _SonicBrandingComprehensiveTestState extends State<SonicBrandingComprehensiveTest> {
  String _testResults = '';
  bool _isTestingInProgress = false;

  void _addTestResult(String result) {
    setState(() {
      _testResults += '$result\n';
    });
    if (kDebugMode) {
      print('🧪 Test Result: $result');
    }
  }

  void _clearResults() {
    setState(() {
      _testResults = '';
    });
  }

  Future<void> _runComprehensiveTest() async {
    setState(() {
      _isTestingInProgress = true;
      _testResults = '';
    });

    _addTestResult('🧪 Starting Comprehensive Sonic Branding Test...');
    
    // Test 1: Payment Success Sound
    _addTestResult('🎵 Test 1: Testing Payment Success Sound...');
    try {
      await SonicBrandingService.playPaymentSuccessSound();
      _addTestResult('✅ Payment Success Sound - PASSED');
    } catch (e) {
      _addTestResult('❌ Payment Success Sound - FAILED: $e');
    }

    await Future.delayed(Duration(seconds: 2));

    // Test 2: Custom Notification Sound
    _addTestResult('🎵 Test 2: Testing Custom Notification Sound...');
    try {
      await SonicBrandingService.playCustomSound('custom_notification');
      _addTestResult('✅ Custom Notification Sound - PASSED');
    } catch (e) {
      _addTestResult('❌ Custom Notification Sound - FAILED: $e');
    }

    await Future.delayed(Duration(seconds: 2));

    // Test 3: Landline Sound
    _addTestResult('🎵 Test 3: Testing Landline Sound...');
    try {
      await SonicBrandingService.playCustomSound('landline');
      _addTestResult('✅ Landline Sound - PASSED');
    } catch (e) {
      _addTestResult('❌ Landline Sound - FAILED: $e');
    }

    await Future.delayed(Duration(seconds: 2));

    // Test 4: Non-existent Sound (should fallback gracefully)
    _addTestResult('🎵 Test 4: Testing Non-existent Sound (Fallback Test)...');
    try {
      await SonicBrandingService.playCustomSound('non_existent_sound');
      _addTestResult('✅ Non-existent Sound Fallback - PASSED');
    } catch (e) {
      _addTestResult('❌ Non-existent Sound Fallback - FAILED: $e');
    }

    await Future.delayed(Duration(seconds: 2));

    // Test 5: Stop Sound
    _addTestResult('🎵 Test 5: Testing Stop Sound...');
    try {
      await SonicBrandingService.stopSound();
      _addTestResult('✅ Stop Sound - PASSED');
    } catch (e) {
      _addTestResult('❌ Stop Sound - FAILED: $e');
    }

    _addTestResult('🧪 Comprehensive Test Completed!');
    setState(() {
      _isTestingInProgress = false;
    });
  }

  Future<void> _simulatePaymentSuccess() async {
    _addTestResult('💳 Simulating Payment Success Flow...');
    
    // Simulate the exact flow from payment success screen
    await Future.delayed(Duration(milliseconds: 500), () {
      if (kDebugMode) {
        print('🎵 Payment successful! Preparing to play sonic branding audio...');
      }
      SonicBrandingService.playPaymentSuccessSound();
    });
    
    _addTestResult('✅ Payment Success Flow Simulation - COMPLETED');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Sonic Branding Comprehensive Test'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Sonic Branding Audio Test Suite',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            
            // Test Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isTestingInProgress ? null : _runComprehensiveTest,
                    child: _isTestingInProgress 
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                              SizedBox(width: 8),
                              Text('Testing...'),
                            ],
                          )
                        : Text('Run Full Test Suite'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepPurple,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 15),
                    ),
                  ),
                ),
                SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _simulatePaymentSuccess,
                    child: Text('Simulate Payment Success'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 15),
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 10),
            
            // Individual Test Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _addTestResult('🎵 Playing Payment Success Sound...');
                      SonicBrandingService.playPaymentSuccessSound();
                    },
                    child: Text('Payment Success'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                SizedBox(width: 5),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _addTestResult('🎵 Playing Custom Notification...');
                      SonicBrandingService.playCustomSound('custom_notification');
                    },
                    child: Text('Notification'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                SizedBox(width: 5),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _addTestResult('🔇 Stopping Sound...');
                      SonicBrandingService.stopSound();
                    },
                    child: Text('Stop'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _clearResults,
              child: Text('Clear Results'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                foregroundColor: Colors.white,
              ),
            ),
            
            SizedBox(height: 20),
            
            // Test Results
            Text(
              'Test Results:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10),
            
            Expanded(
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[50],
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResults.isEmpty ? 'No test results yet. Run a test to see results.' : _testResults,
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
            
            SizedBox(height: 10),
            
            Text(
              'Instructions:\n'
              '• Run "Full Test Suite" to test all audio functionality\n'
              '• Use individual buttons to test specific sounds\n'
              '• Check device volume and ensure audio is enabled\n'
              '• Look for audio logs in the debug console',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }
}
