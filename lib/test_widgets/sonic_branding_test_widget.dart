import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:sso_futurescape/services/sonic_branding_service.dart';

/// Test widget to verify sonic branding functionality
class SonicBrandingTestWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Sonic Branding Test'),
        backgroundColor: Colors.blue,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Sonic Branding Audio Test',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                if (kDebugMode) {
                  print('🎵 Test: Playing payment success sound...');
                }
                SonicBrandingService.playPaymentSuccessSound();
              },
              child: Text('Play Payment Success Sound'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                if (kDebugMode) {
                  print('🎵 Test: Playing custom notification sound...');
                }
                SonicBrandingService.playCustomSound('custom_notification');
              },
              child: Text('Play Custom Notification'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                if (kDebugMode) {
                  print('🎵 Test: Playing landline sound...');
                }
                SonicBrandingService.playCustomSound('landline');
              },
              child: Text('Play Landline Sound'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                if (kDebugMode) {
                  print('🎵 Test: Stopping all sounds...');
                }
                SonicBrandingService.stopSound();
              },
              child: Text('Stop Sound'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
            ),
            SizedBox(height: 30),
            Text(
              'Check the debug console for audio playback logs',
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
