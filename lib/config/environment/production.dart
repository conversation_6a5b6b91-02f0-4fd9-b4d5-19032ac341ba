import 'package:material_symbols_icons/material_symbols_icons.dart';
import 'package:sso_futurescape/config/environment/environment.dart';
import 'package:sso_futurescape/config/strings/strings.dart';
import 'package:sso_futurescape/ui/new_sso/new_sso_constants.dart';

import 'config.dart';

class Production extends Config {
  Production() {
    // FirebaseDatabaseUtils.environment = "Live";
  }

  @override
  get build_variant => Environments.PRODUCTION;

  @override
  get razorpayApiKey => "rzp_live_sFvCAU7miy1Gvh";

  @override
  get url => "chsone.in";

  @override
  get chsoneApiUrl => "https://api.chsone.in/";

  @override
  get newGateBaseUrl => NewSsoConstants.gateBaseDomain;

  @override
  get mobikwikUtilitiesPaymentApiUrl => "https://b2b.mobikwik.com/";

  @override
  get mobikwikUtilitiesMerchantId => "fs101";

  @override
  get mobikwikUtilities => [
        {
          "name": FsString.FASTAG_RECHARGE,
          "img": Symbols.barcode_scanner,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.PREPAID_MOBILE_RECHARGE,
          "img": Symbols.smartphone,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.POSTPAID_MOBILE_RECHARGE,
          "img": Symbols.smartphone,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        // {
        //   "name": FsString.DATACARD_RECHARGE,
        //   "img": Symbols.security_key,
        //   "active": true,
        //   "url": mobikwikUtilitiesPaymentApiUrl +
        //       "prepaid-recharge?mid=" +
        //       mobikwikUtilitiesMerchantId
        // },

        {
          "name": "Bill Pay",
          "img":
              "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/B_mnemonic_PNG_fc337fa80a.png",
          "active": true,
          "url": "bharatconnect",
        },
        {
          "name": FsString.ELECTRICITY_RECHARGE,
          "img": Symbols.bolt,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        // {
        //   "name": FsString.DTH_RECHARGE,
        //   "img": Symbols.satellite_alt,
        //   "active": true,
        //   "url": mobikwikUtilitiesPaymentApiUrl +
        //       "dth-recharge?mid=" +
        //       mobikwikUtilitiesMerchantId
        // },
        {
          "name": FsString.GAS_BILL,
          "img": Symbols.valve,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.LPG_BOOKING,
          "img": Symbols.propane_tank,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.LANDLINE_BILL,
          "img": Symbols.deskphone,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.INSURANCE,
          "img": Symbols.health_and_safety,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.WATER_BILL,
          "img": Symbols.water_drop,
          "location": "",
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.BROADBAND_RECHARGE,
          "img": Symbols.router,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.EMI_PAYMENT,
          "img": Symbols.credit_score,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.CABLE,
          "img": Symbols.desktop_windows,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.MUNICIPALITY,
          "img": Symbols.account_balance,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.DIGITAL_VOUCHAR,
          "img": Symbols.shop,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.RENT_PAYMENT,
          "img": Symbols.house,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
        {
          "name": FsString.MAINTENANCE,
          "img": Symbols.real_estate_agent,
          "active": true,
          "url": mobikwikUtilitiesPaymentApiUrl +
              "prepaid-recharge?mid=" +
              mobikwikUtilitiesMerchantId
        },
      ];

  @override
  get chsoneClientId => "Epm9rkpUrbSSByBD";

  @override
  get chsoneClientSecret => "Nk68pdu6kLFHTAAH";

  @override
  get ssoApiKey =>
      "5b2f4b31f524b6603d81dbcaabd8121078d00f1614a294c3207e39951f7054c8";

  @override
  get ssoAuthUrl =>
      "https://authapi.chsone.in/api/v2/"; // Rohit changed http to https on 11th september 2024

  @override
  get ssoClientId => "web_client";

  @override
  get ssoClientSecret => "ZMsRmR2vXH5Q2one";

  @override
  get chsoneResidentUrl => "https://api.chsone.in/residentapi/v2/";

  @override
  get chsoneOperatorsUrl => "https://chsone.in/api/v1/";

  @override
  get newOnePayBaseUrl => "https://apigw.cubeone.in/onepay-live/api/v2/";

  // @override
  // get onebookApiBaseUrl => "http://cmsaccount.cubeonebiz.com/";

  @override
  get onebookHeaderAuthorization => "Basic ********************************";

  @override
  get paymentHistoryUrl =>
      "https://apigw.cubeone.in/onepay/api/v1/listutilitybillstxns";

  @override
  get vizlogAppUrl =>
      "https://gateapi.cubeone.biz/api/v1/"; // Rohit Jain changed http to https on 11th september 2024

  @override
  get chsoneAppId => "2";

  @override
  get hrmsAppId => "16";

  @override
  get vizlogAppId => "5";

  // @override
  // get restoApiUrl => "http://crmapi.vezaone.com/api/v1/";

  @override
  get vizlogClientId => "vizlog_android";

  @override
  get vizlogClientSecret => "giT16P1p3U";

  @override
  get chsoneWebUrl => "https://chsone.in/";

  @override
  get crm_api_key =>
      "5c57fdbef9c432e61e0d1950f19db0c4d35eb6983236b17ebd82ab2578cc794e";

  /*@override
  get tiffin_image_logo =>
      'http://s3-ap-southeast-1.amazonaws.com/fdc01.fooddialer.in';*/
  @override
  get tiffin_image_logo => 'https://s3.ap-south-1.amazonaws.com/cubeoneapp.com';

  @override
  get vezaPlugInUrl =>
      "https://demo.vezaone.com/"; // Rohit changed http to https on 11th september 2024

  @override
  setUpVersions() {
    return {"android": 1.0, "ios": 1.0, "web": 1.0};
  }

  @override
  get getOneAppId => "15";

  /* @override
  get vezaPlugInUrl => throw UnimplementedError();*/

  @override
  get chsoneRequestUrl =>
      "https://society.cubeonebiz.com/marketplaces/v1/oneapp-request-demo"; // Rohit changed http to https on 11th september 2024

  @override
  get hrmsClientId => 'web_client_1';

  @override
  get hrmsClientSecret => 'Z7s4AIXv';

  @override
  get vezaGroceryPlugInUrl => "https://retail.vezaone.com/";

  @override
  get name => Environments.PRODUCTION;

  @override
  get account_url => 'https://account.cubeoneapp.com/login';

  @override
  // get nodeUrl => "https://visitapi.cubeone.biz/";
  get nodeUrl => "https://visitapi.cubeone.biz/";

  @override
  get ecollectKey => "srvybecollect";

  @override
  get subscriptionPlugInUrl => "https://subscription.vezaone.com/";

  @override
  get notificationUrl =>
      "https://notifications.vezaone.com/"; // Rohit changed http to https on 11th september 2024

  @override
  get pyna_wine_company_Id => "3824";

  @override
  get recipeUrl => "https://recipe.vezaone.com";

  @override
  get serverApiKey =>
      "f22bddda7e359f62dd908f79e6ea210a4c7a364b3a820108456fdb44842ad099";

  @override
  get moneyUrl => "https://moneyapi.vezaone.com/api/v1/";

  @override
  get meetingBaseUrl => "https://meet.cubeonebiz.com/api/v1/";

  // rohit have commented this because this is not in use 11th september 2024
  // @override
  // get paymentBaseUrl => "https://pay.cubeone.com/api/v1/";

  @override
  get meetingClientSecret => "MlJBFR8Ko9";

  @override
  get meetingClientId => "meeting_web";

  @override
  get meetingDomain => "collab.cubeone.in";

  // @overrides
  // // ignore: override_on_non_overriding_member
  // get utilityBillDomain => "pay.cubeone.com";

  @override
  get meetingAppId => 17;

  @override
  get gateDomain => "gate.cubeonebiz.com";

  // @override
  // get iplUrl => "https://quiz.cubeonebiz.com/api/v1/";

  @override
  get meetingCryptoKey => "rahultoSecretKey";

  @override
  get meetingUriScheme => "meet";

  @override
  get eboardUrl => 'https://eboardapi.vezaone.com/api/v1/';

  @override
  // get payBill => "https://chsone.in/api/v1/utilitybills/pay";
  get payBill => "https://apigw.cubeone.in/onepay-live/api/v2/utilitybills/pay";

  @override
  get ssoUrl => "https://ssoapi.vezaone.com/api/v2/";

  @override
  String get fs_ApiKey =>
      "8cf3d404e23f515ad8d591d7cc005e1ed25e79838e1dfb1e3c7756a840429af6";

  @override
  get adsBaseUrl => "https://publisher.cubeonebiz.com/banner-api.php";

  // @override
  // get onebook_base_url => "http://cmsaccount.cubeonebiz.com/api/";

  @override
  get ssoAccountDeleteUrl =>
      "https://ssoapi.cubeonebiz.com/api/v2/"; // Rohit changed http to https on 11th september 2024
}
