import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class BAssuredLogo extends StatelessWidget {
  final double? height;
  final double? width;

  const BAssuredLogo({
    Key? key,
    this.height = 40,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: CachedNetworkImage(
        imageUrl:
            "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/B_Assured_Logo_PNG_transparent_bg.png",
        placeholder: (context, url) => SizedBox(
          height: height,
          width: width,
          child: Center(
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
        errorWidget: (context, url, error) => _buildFallbackLogo(),
        height: height,
        width: width,
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _buildFallbackLogo() {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.blue.shade200, width: 1),
      ),
      child: Center(
        child: Text(
          'B\nAssured',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: (height ?? 40) * 0.25,
            fontWeight: FontWeight.bold,
            color: Colors.blue.shade700,
            height: 1.0,
          ),
        ),
      ),
    );
  }
}
