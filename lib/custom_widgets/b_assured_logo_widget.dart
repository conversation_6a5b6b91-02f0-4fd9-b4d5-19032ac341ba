import 'package:flutter/material.dart';

class BAssuredLogo extends StatelessWidget {
  final double? height;
  final double? width;

  const BAssuredLogo({
    Key? key,
    this.height = 60,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: _buildSvgLogo(),
    );
  }

  Widget _buildSvgLogo() {
    return FutureBuilder<Widget>(
      future: _loadPngLogo(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return snapshot.data!;
        } else {
          // Show visible placeholder while loading
          return _buildVisiblePlaceholder();
        }
      },
    );
  }

  Future<Widget> _loadPngLogo() async {
    try {
      return Image.asset(
        'assets/images/b_assured.png',
        height: height,
        width: width,
        fit: BoxFit.contain,
        filterQuality: FilterQuality.high,
        isAntiAlias: true,
        errorBuilder: (context, error, stackTrace) {
          return _buildVisiblePlaceholder();
        },
      );
    } catch (e) {
      // If P<PERSON> fails, return the visible placeholder
      return _buildVisiblePlaceholder();
    }
  }

  Widget _buildVisiblePlaceholder() {
    return Container(
      height: height ?? 60,
      width: width ?? 100,
      decoration: BoxDecoration(
        color: Colors.blue.shade600,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white, width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 3,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'B\nAssured',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: (height ?? 60) * 0.25,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            height: 1.1,
            letterSpacing: 0.5,
          ),
        ),
      ),
    );
  }
}
