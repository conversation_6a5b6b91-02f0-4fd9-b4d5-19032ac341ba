import 'package:flutter/material.dart';

class BAssuredLogo extends StatelessWidget {
  final double? height;
  final double? width;

  const BAssuredLogo({
    Key? key,
    this.height = 40,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: _buildVisibleLogo(),
    );
  }

  Widget _buildVisibleLogo() {
    // For now, let's use a highly visible placeholder that definitely shows
    // This ensures the logo space is always visible while we debug SVG loading
    return Container(
      height: height ?? 35,
      width: width ?? 80,
      decoration: BoxDecoration(
        color: Colors.blue.shade600,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.white, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'B\nAssured',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: (height ?? 35) * 0.22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            height: 1.1,
          ),
        ),
      ),
    );
  }
}
