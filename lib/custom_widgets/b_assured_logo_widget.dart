import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class BAssuredLogo extends StatelessWidget {
  final double? height;
  final double? width;

  const BAssuredLogo({
    Key? key,
    this.height = 40,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: _buildSvgLogo(),
    );
  }

  Widget _buildSvgLogo() {
    return FutureBuilder<Widget>(
      future: _loadSvgLogo(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return snapshot.data!;
        } else {
          // Show visible placeholder while loading
          return _buildVisiblePlaceholder();
        }
      },
    );
  }

  Future<Widget> _loadSvgLogo() async {
    try {
      return SvgPicture.asset(
        'assets/images/b_assured_logo.svg',
        height: height,
        width: width,
        fit: BoxFit.contain,
      );
    } catch (e) {
      // If SVG fails, return the visible placeholder
      return _buildVisiblePlaceholder();
    }
  }

  Widget _buildVisiblePlaceholder() {
    return Container(
      height: height ?? 35,
      width: width ?? 80,
      decoration: BoxDecoration(
        color: Colors.blue.shade600,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.white, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'B\nAssured',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: (height ?? 35) * 0.22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            height: 1.1,
          ),
        ),
      ),
    );
  }
}
