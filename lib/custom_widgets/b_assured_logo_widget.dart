import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class BAssuredLogo extends StatelessWidget {
  final double? height;
  final double? width;

  const BAssuredLogo({
    Key? key,
    this.height = 40,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: CachedNetworkImage(
        imageUrl:
            "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/B_Assured_Logo_PNG_transparent_bg.png",
        placeholder: (context, url) => CircularProgressIndicator(),
        errorWidget: (context, url, error) => Image.asset(
          'assets/images/b_assured.png',
          height: height,
          width: width,
        ),
        height: height,
        width: width,
      ),
    );
  }
}
