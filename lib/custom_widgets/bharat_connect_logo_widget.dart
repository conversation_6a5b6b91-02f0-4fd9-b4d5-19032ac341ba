import 'package:flutter/material.dart';

class BharatConnectLogo extends StatelessWidget {
  final double? height;
  final double? width;

  const BharatConnectLogo({
    Key? key,
    this.height = 40,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: _buildLogo(),
    );
  }

  Widget _buildLogo() {
    return FutureBuilder<Widget>(
      future: _loadPngLogo(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return snapshot.data!;
        } else {
          // Show visible placeholder while loading
          return _buildVisiblePlaceholder();
        }
      },
    );
  }

  Future<Widget> _loadPngLogo() async {
    try {
      return Image.asset(
        'assets/images/Bharat_Connect_Transparent_Logo_PNG_transparent_bg.png',
        height: height,
        width: width,
        fit: BoxFit.contain,
        filterQuality: FilterQuality.high,
        isAntiAlias: true,
        errorBuilder: (context, error, stackTrace) {
          return _buildVisiblePlaceholder();
        },
      );
    } catch (e) {
      // If P<PERSON> fails, return the visible placeholder
      return _buildVisiblePlaceholder();
    }
  }

  Widget _buildVisiblePlaceholder() {
    return Container(
      height: height ?? 40,
      width: width ?? 80,
      decoration: BoxDecoration(
        color: Colors.orange.shade600,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.white, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'Bharat\nConnect',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: (height ?? 40) * 0.18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            height: 1.1,
            letterSpacing: 0.3,
          ),
        ),
      ),
    );
  }
}
