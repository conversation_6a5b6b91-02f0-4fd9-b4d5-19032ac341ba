import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class SecuredByBBPS extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: CachedNetworkImage(
        imageUrl:
            "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/Bharat_Connect_Primary_Logo_PNG_5cb35a279e.png",
        placeholder: (context, url) => CircularProgressIndicator(),
        errorWidget: (context, url, error) => Icon(Icons.error),
        height: 40,
      ),

      // Image.asset(
      //   "assets/images/bbps_logo.png",
      //   height: 24,
      // ),
    );

    //   Container(
    //   color: Theme.of(context).colorScheme.surface,
    //   height: 55,
    //   width: MediaQuery.of(context).size.width * 0.9,
    //   child: Row(
    //     children: [
    //       Spacer(),
    //       Text("secured by"),
    //       Padding(
    //         padding: const EdgeInsets.only(left: 8.0),
    //         child: Image.asset(
    //           "assets/images/bbps_logo.png",
    //           width: 80,
    //           height: 50,
    //         ),
    //       ),
    //       Spacer(),
    //     ],
    //   ),
    // );
  }
}
