import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class SecuredByBBPS extends StatelessWidget {
  final double? height;
  final double? width;

  const SecuredByBBPS({
    Key? key,
    this.height = 40,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: CachedNetworkImage(
        imageUrl:
            "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/Bharat_Connect_Transparent_Logo_PNG_transparent_bg.png",
        placeholder: (context, url) => SizedBox(
          height: height,
          width: width,
          child: Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
            ),
          ),
        ),
        errorWidget: (context, url, error) => Image.asset(
          'assets/images/bbps_logo.png',
          height: height,
          width: width,
          fit: BoxFit.contain,
        ),
        height: height,
        width: width,
        fit: BoxFit.contain,
      ),
    );
  }
}
