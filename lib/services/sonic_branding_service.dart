import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class SonicBrandingService {
  static const MethodChannel _channel = MethodChannel('sonic_branding');

  /// Play payment success sonic branding audio
  static Future<bool> playPaymentSuccessSound() async {
    try {
      await Future.delayed(Duration(milliseconds: 500)); // 500ms delay as required
      final bool result = await _channel.invokeMethod('playPaymentSuccessSound');
      if (kDebugMode) {
        print('🔊 Payment success sonic branding played successfully');
      }
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error playing payment success sound: $e');
      }
      return false;
    }
  }

  /// Play custom notification sound
  static Future<bool> playCustomNotificationSound() async {
    try {
      final bool result = await _channel.invokeMethod('playCustomNotificationSound');
      if (kDebugMode) {
        print('🔊 Custom notification sound played successfully');
      }
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error playing custom notification sound: $e');
      }
      return false;
    }
  }

  /// Play landline sound
  static Future<bool> playLandlineSound() async {
    try {
      final bool result = await _channel.invokeMethod('playLandlineSound');
      if (kDebugMode) {
        print('🔊 Landline sound played successfully');
      }
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error playing landline sound: $e');
      }
      return false;
    }
  }

  /// Stop all sounds
  static Future<bool> stopSound() async {
    try {
      final bool result = await _channel.invokeMethod('stopSound');
      if (kDebugMode) {
        print('🔇 All sounds stopped');
      }
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error stopping sound: $e');
      }
      return false;
    }
  }

  /// Test sound with fallback
  static Future<bool> testSoundWithFallback() async {
    try {
      final bool result = await _channel.invokeMethod('testSoundWithFallback');
      if (kDebugMode) {
        print('🔊 Test sound with fallback played successfully');
      }
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error playing test sound with fallback: $e');
      }
      return false;
    }
  }
}
