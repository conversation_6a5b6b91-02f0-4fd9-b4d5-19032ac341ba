import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class SonicBrandingService {
  static const MethodChannel _channel = MethodChannel('com.cubeone.app/audio');

  /// Play the sonic branding audio for payment success
  static Future<void> playPaymentSuccessSound() async {
    try {
      await _channel.invokeMethod('playSound', {
        'soundName': 'payment_success_sonic',
      });
      if (kDebugMode) {
        print('🔊 Payment success sonic branding played successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error playing payment success sonic branding: $e');
      }
      // Fallback to default notification sound
      try {
        await _channel.invokeMethod('playSound', {
          'soundName': 'landline',
        });
        if (kDebugMode) {
          print('🔊 Fallback sound played successfully');
        }
      } catch (fallbackError) {
        if (kDebugMode) {
          print('⚠️ Error playing fallback sound: $fallbackError');
        }
      }
    }
  }

  /// Stop any currently playing sound
  static Future<void> stopSound() async {
    try {
      await _channel.invokeMethod('stopSound');
      if (kDebugMode) {
        print('🔇 Sonic branding sound stopped');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error stopping sonic branding sound: $e');
      }
    }
  }

  /// Play a custom sound by name
  static Future<void> playCustomSound(String soundName) async {
    try {
      await _channel.invokeMethod('playSound', {
        'soundName': soundName,
      });
      if (kDebugMode) {
        print('🔊 Custom sound "$soundName" played successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error playing custom sound "$soundName": $e');
      }
    }
  }
}
