import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class SonicBrandingService {
  static const MethodChannel _channel =
      MethodChannel('notification_sound_channel');

  /// Play the sonic branding audio for payment success
  static Future<void> playPaymentSuccessSound() async {
    try {
      // First try to play the custom payment success sound
      await _channel.invokeMethod('playSound', {
        'soundName': 'payment_success_sonic',
      });
      if (kDebugMode) {
        print('🔊 Payment success sonic branding played successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error playing payment success sonic branding: $e');
      }
      // Fallback to custom notification sound for payment success
      try {
        await _channel.invokeMethod('playSound', {
          'soundName': 'custom_notification',
        });
        if (kDebugMode) {
          print('🔊 Payment success fallback sound played successfully');
        }
      } catch (fallbackError) {
        if (kDebugMode) {
          print('⚠️ Error playing fallback sound: $fallbackError');
        }
        // Final fallback to landline sound
        try {
          await _channel.invokeMethod('playSound', {
            'soundName': 'landline',
          });
          if (kDebugMode) {
            print('🔊 Final fallback sound played successfully');
          }
        } catch (finalError) {
          if (kDebugMode) {
            print('⚠️ Error playing final fallback sound: $finalError');
          }
        }
      }
    }
  }

  /// Stop any currently playing sound
  static Future<void> stopSound() async {
    try {
      await _channel.invokeMethod('stopSound');
      if (kDebugMode) {
        print('🔇 Sonic branding sound stopped');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error stopping sonic branding sound: $e');
      }
    }
  }

  /// Play a custom sound by name
  static Future<void> playCustomSound(String soundName) async {
    try {
      await _channel.invokeMethod('playSound', {
        'soundName': soundName,
      });
      if (kDebugMode) {
        print('🔊 Custom sound "$soundName" played successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error playing custom sound "$soundName": $e');
      }
    }
  }
}
