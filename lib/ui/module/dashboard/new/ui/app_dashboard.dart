import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:common_config/utils/application.dart';
import 'package:common_config/utils/firebase/firebase_dynamiclink.dart';
import 'package:common_config/utils/fs_navigator.dart';
import 'package:confetti/confetti.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_update/in_app_update.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:sso_futurescape/config/environment/environment.dart';
import 'package:sso_futurescape/config/strings/strings.dart';
import 'package:sso_futurescape/custom_widgets/custom_elevated_btn.dart';
import 'package:sso_futurescape/presentor/module/member_invitation/member_invitation_presenter.dart';
import 'package:sso_futurescape/presentor/module/member_invitation/member_invitation_view.dart';
import 'package:sso_futurescape/presentor/module/member_invitation/notification_presenter.dart';
import 'package:sso_futurescape/presentor/module/member_invitation/notification_view.dart';
import 'package:sso_futurescape/presentor/module/sso/profile/profile_model.dart';
import 'package:sso_futurescape/providers/card_postion_provider.dart';
import 'package:sso_futurescape/theme/theme.dart';
import 'package:sso_futurescape/ui/module/apartment/delegates/apartment_notification_action_delegate.dart';
import 'package:sso_futurescape/ui/module/chsone/myflats_invitees.dart';
import 'package:sso_futurescape/ui/module/dashboard/new/helper/dashboard_helper.dart';
import 'package:sso_futurescape/ui/module/dashboard/new/ui/cards/dashboard_card.dart';
import 'package:sso_futurescape/ui/module/diwali/diwali_main.dart';
import 'package:sso_futurescape/ui/module/ipl/ipl_card.dart';
import 'package:sso_futurescape/ui/module/ipl/ipl_main.dart';
import 'package:sso_futurescape/ui/module/meeting/delegate/meeting_notification_action_delegate.dart';
import 'package:sso_futurescape/ui/module/meeting/details/meetvote_main.dart';
import 'package:sso_futurescape/ui/module/meeting/utils/meet_vote_utils.dart';
import 'package:sso_futurescape/ui/module/orders/store_detail_helper.dart';
import 'package:sso_futurescape/ui/module/pyna/delegate/pyna_notification_action_delegate.dart';
import 'package:sso_futurescape/ui/module/recipe/services/http_service.dart';
import 'package:sso_futurescape/ui/module/sso/dashboard/app_update/helper/new_update_helper.dart';
import 'package:sso_futurescape/ui/module/sso/dashboard/my_business.dart';
import 'package:sso_futurescape/ui/module/sso/dashboard/vizlog_card.dart';
import 'package:sso_futurescape/ui/module/sso/profile/profile_view.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/payment_notification_action_delegate.dart';
import 'package:sso_futurescape/services/sonic_branding_service.dart';
import 'package:sso_futurescape/test_widgets/sonic_branding_comprehensive_test.dart';
import 'package:sso_futurescape/ui/module/vizlog/intercom/messaging/helper/messaging_notification_action_delegate.dart';
import 'package:sso_futurescape/ui/module/vizlog/intercom/messaging_constants.dart';
import 'package:sso_futurescape/ui/module/vizlog/myguest/myguest_invitations/new_guest_invitation_action_delegate.dart';
import 'package:sso_futurescape/ui/new_sso/network/api_service.dart';
import 'package:sso_futurescape/utils/app_utils.dart';
import 'package:sso_futurescape/utils/base_loader_utils.dart';
import 'package:sso_futurescape/utils/firebase_util/firebase_notification.dart';
import 'package:sso_futurescape/utils/fsshare.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';
import 'package:sso_futurescape/utils/user_util.dart';
import 'package:sso_futurescape/utils/widget_utils.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:version/version.dart';

import '../../../chsone/card/new/one_society.dart';
import '../../../meeting/card/one_meet.dart';
import '../../../vizlog/notification/delegate/vizlog_notification_action_delegate.dart';

class DataService {
  Future<List<Map<String, dynamic>>> loadData() async {
    String data = await rootBundle.loadString('assets/home_ads.json');
    List<dynamic> jsonData = json.decode(data);
    return jsonData.cast<Map<String, dynamic>>();
  }
}

class HomeTab extends StatefulWidget {
  bool isDirectToNotification = true;

  HomeTab({this.isDirectToNotification = true});

  @override
  AppDashboardState createState() => AppDashboardState();
}

class AppDashboardState extends State<HomeTab>
    with
        TickerProviderStateMixin,
        AutomaticKeepAliveClientMixin,
        WidgetsBindingObserver
    implements MemberInvitationView, NotificationView {
  FirebaseNotifications firebaseNotifications = FirebaseNotifications();

  static const platform_channel =
      const MethodChannel('app.channel.shared.data');
  MethodChannel platform = const MethodChannel('com.cubeone.app/MainActivity');
  TabController? _tabController;

  String profileUrl = "";
  int counter = 0;
  String cartCount = "0";
  bool isBusinessMode = false;
  bool _confettiShown = false;
  List notificationList = [];
  static bool isWineShow = false;
  bool _isUpdateAvailable = false;
  String? iosLatestVersion;
  String? androidLatestVersion;
  int? notifications = 0;
  int notificationCount = 0;
  NotificationPresenter? invitationPresenter;
  var onlineOrderPresenter;
  dynamic _userProfile;
  late AnimationController _controller;
  late Animation<double?> _animation;

  late ConfettiController _controllerCenter;

  Path drawStar(Size size) {
    double degToRad(double deg) => deg * (pi / 180.0);

    const numberOfPoints = 5;
    final halfWidth = size.width / 2;
    final externalRadius = halfWidth;
    final internalRadius = halfWidth / 2.5;
    final degreesPerStep = degToRad(360 / numberOfPoints);
    final halfDegreesPerStep = degreesPerStep / 2;
    final path = Path();
    final fullAngle = degToRad(360);

    path.moveTo(size.width, halfWidth);

    for (double step = 0; step < fullAngle; step += degreesPerStep) {
      path.lineTo(halfWidth + externalRadius * cos(step),
          halfWidth + externalRadius * sin(step));
      path.lineTo(halfWidth + internalRadius * cos(step + halfDegreesPerStep),
          halfWidth + internalRadius * sin(step + halfDegreesPerStep));
    }
    path.close();
    return path;
  }

  late Future<List<Map<String, dynamic>>> _futureData;

  List<Widget> widgets = [
    OneSociety(),
    VizlogCard(),
    OneMeet(),
  ];

  @override
  void initState() {
    super.initState();
    // KeycloakSSOApiService().fetchDecryptAndStoreOnePayConfig();
    KeycloakSSOApiService().fetchAndStoreOnePayConfig();
    KeycloakSSOApiService().fetchGateBaseDomain();
    print("fetchDecryptAndStoreOnePayConfig");
    // encrptedToked();
    firebaseNotifications.getFcmToken().then((token) {
      if (kDebugMode) {
        print('Device Token: $token');
      }
      if (token != null) {
        HttpService.addDevice(token);
        KeycloakSSOApiService keycloakSSOApiService = KeycloakSSOApiService();

        keycloakSSOApiService.sendDeviceTokenToServer(token);
      }
    });

    // Register lifecycle observer
    WidgetsBinding.instance.addObserver(this);
    // KeycloakSSOApiService().fetchOnePayKey();

    // Fetch data and initialize required components
    fetchAndroidUpdateNotes();
    getVersionNumber();
    _futureData = DataService().loadData();
    _loadBusinessMode();
    _initNewUpdateDialogVisibility();
    _passNodeUrl();
    _loadNotificationCount();

    // Handle Firebase notifications and dynamic links
    FirebaseNotifications().firebaseCloudMessaging_Listeners(
      context,
      onRedirected: onRedirected,
    );
    FirebaseDynamicLink.handleDynamicLinks(
      context,
      handleLinkData,
    );

    // Initialize shared text if available
    initPlatformState();
    getSharedText();

    // Initialize controllers
    _tabController = TabController(length: 3, vsync: this);
    _controllerCenter = ConfettiController(
      duration: const Duration(seconds: 2),
    )..play();

    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 10),
    )..repeat(reverse: false);

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    // Check for new updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        _checkNewUpdateAvailability(context);
      } catch (e) {
        print("Error checking for updates: $e");
      }
    });
    _getAndSaveKeyclockToken();
  }

  late ProfileModel profileModel;

  void _getAndSaveKeyclockToken() {
    try {
      developer.log("Attempting to get Keycloak token...");
      KeycloakSSOApiService().getKeycloakToken().then((value) {
        developer.log("Keycloak token received successfully");
        // Here you can save the token to storage or state
        // For example:
        // SsoStorage.saveKeycloakToken(value['access_token']);
      }).catchError((e) {
        developer.log("Error getting keycloak token: $e");
        // Handle error silently to prevent app crashes
      });
    } catch (e) {
      developer.log("Exception in _getAndSaveKeyclockToken: $e");
      // Prevent crashes by handling exceptions here
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      checkForUpdate();
      _loadNotificationCount();
      _futureData = DataService().loadData(); // Reload data
      setState(() {});
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    checkForUpdate();
    _loadNotificationCount();
    _futureData = DataService().loadData();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controllerCenter.dispose();
    _controller.dispose();
    super.dispose();
  }

  String newVersionNumber = "";

  Future<void> getVersionNumber() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      newVersionNumber = packageInfo.version;
    });
  }

  Future<void> checkForUpdate() async {
    if (Theme.of(context).platform == TargetPlatform.android) {
      try {
        //solvethis
        final AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();
        if (updateInfo.updateAvailability ==
            UpdateAvailability.updateAvailable) {
          setState(() {
            _isUpdateAvailable = true;
          });
        }
      } catch (e) {
        print("Error checking for update: $e");
      }
    } else if (Theme.of(context).platform == TargetPlatform.iOS) {
      final bool isUpdateAvailable = await checkForUpdateOnIOS();
      if (isUpdateAvailable) {
        setState(() {
          _isUpdateAvailable = true;
        });
      }
    }
  }

  Future<bool> checkForUpdateOnIOS() async {
    const String iOSAppId = "1492930711";

    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      String currentVersion = packageInfo.version;

      final response = await http.get(
        Uri.parse('https://itunes.apple.com/lookup?id=$iOSAppId'),
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> data = json.decode(response.body);
        if (data['resultCount'] > 0) {
          iosLatestVersion = data['results'][0]['version'];
          print("latestVersion : $iosLatestVersion");
          if (Version.parse(currentVersion) <
              Version.parse(iosLatestVersion!)) {
            return true;
          }
        }
      }
    } catch (e) {
      print("Error checking for update: $e");
    }

    return false;
  }

  void _performUpdate() async {
    if (Theme.of(context).platform == TargetPlatform.android) {
      try {
        await InAppUpdate.performImmediateUpdate();
        setState(() {
          _isUpdateAvailable = false;
        });
      } catch (e) {
        print("Error while performing in-app update: $e");
      }
    } else if (Theme.of(context).platform == TargetPlatform.iOS) {
      const url = 'https://apps.apple.com/app/1492930711';
      if (await canLaunch(url)) {
        await launch(url);
        setState(() {
          _isUpdateAvailable = false;
        });
      } else {
        print("Could not launch App Store.");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      children: [
        // SizedBox(
        //   // margin: EdgeInsets.only(top: 10),
        //   height: MediaQuery.of(context).size.height * 0.2,
        //   child: FutureBuilder(
        //     future: DataService().loadData(),
        //     builder:
        //         (context, AsyncSnapshot<List<Map<String, dynamic>>> snapshot) {
        //       if (snapshot.connectionState == ConnectionState.active ||
        //           snapshot.connectionState == ConnectionState.waiting) {
        //         return CircularProgressIndicator();
        //       } else if (snapshot.hasError) {
        //         return Container(
        //           height: MediaQuery.of(context).size.height * 0.2,
        //           child: Center(
        //             child: Text('Error: ${snapshot.error}'),
        //           ),
        //         );
        //       } else {
        //         return MyCarousel(jsonData: snapshot.data!);
        //       }
        //     },
        //   ),
        // ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: _buildContent(context),
        ),
      ],
    );
  }

  Widget _buildConfettiContainer(BuildContext context) {
    Future<bool> coffettieShown = isCoffettieShown();

    if (coffettieShown == false) {
      return Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        child: Align(
          alignment: Alignment.center,
          child: ConfettiWidget(
            confettiController: _controllerCenter,
            blastDirectionality: BlastDirectionality.explosive,
            shouldLoop: false,
            maxBlastForce: 50,
            gravity: 0.1,
            colors: const [
              Colors.green,
              Colors.blue,
              Colors.pink,
              Colors.orange,
              Colors.purple
            ],
            createParticlePath: drawStar,
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  Future<bool> isCoffettieShown() {
    return Future<bool>(() async {
      String? result = await SsoStorage.isCoffetiShown();
      return ("true" == result);
    });
  }

  Widget _buildContent(BuildContext context) {
    return Consumer<CardPositionProvider>(
      builder: (context, provider, _) {
        return Column(
          children: [
            provider.getWidgetForSection('onepay', context),
            provider.getWidgetForSection('one_society', context),
            provider.getWidgetForSection('one_gate', context),
            provider.getWidgetForSection('one_meet', context),
          ],
        );
      },
    );
    // Consumer<CardPositionProvider>(
    //   builder: (context, cardPositionProvider, _) {
    //     return Column(
    //       crossAxisAlignment: CrossAxisAlignment.start,
    //       children: [
    //         Text(
    //           'Household',
    //           style: Theme.of(context).textTheme.headlineMedium,
    //         ),
    //         const SizedBox(height: 8),
    //         ...cardPositionProvider.reorderableItems.map((identifier) {
    //           return cardPositionProvider.getWidgetForIdentifier(
    //               identifier, context);
    //         }),
    //         const SizedBox(height: 20),
    //       ],
    //     );
    //   },
    // );
  }

  // Widget _buildContent(BuildContext context) {
  //   return Consumer<CardPositionProvider>(
  //     builder: (context, cardPositionProvider, _) {
  //       List<Widget> reorderableItems = cardPositionProvider.reorderableItems;
  //       return Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  // Text(
  //   'onepay',
  //   style: Theme.of(context).textTheme.headlineMedium,
  // ),

  // SizedBox(
  //   height: 100,
  //   child: Row(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //     children: [
  //       HomeQuickPay(
  //         text: 'Mobile',
  //         onTap: () {
  //           Navigator.push(
  //             context,
  //             MaterialPageRoute(
  //               builder: (context) => ElectricityBillersList(
  //                 name: FsString.PREPAID_MOBILE_RECHARGE,
  //               ),
  //             ),
  //           );
  //         },
  //       ),
  //       HomeQuickPay(
  //         text: 'Electricity',
  //         icon: Symbols.bolt,
  //         onTap: () {
  //           Navigator.push(
  //             context,
  //             MaterialPageRoute(
  //               builder: (context) => ElectricityBillersList(
  //                 name: "Electricity",
  //               ),
  //             ),
  //           );
  //         },
  //       ),
  //       HomeQuickPay(
  //         text: 'Bill Pay',
  //         // icon: Symbols.valve,
  //         hasBharatConnectLogo: true,
  //         onTap: () {
  //           Navigator.push(
  //             context,
  //             MaterialPageRoute(
  //               builder: (context) => UilityPaymentList(
  //                 source: CommonScreens.NEW_DASHBOARD,
  //               ),
  //             ),
  //           );
  //         },
  //       ),
  //       HomeQuickPay(
  //         text: 'More',
  //         icon: Symbols.north_east,
  //         onTap: () {
  //           Navigator.push(
  //             context,
  //             MaterialPageRoute(
  //               builder: (context) => UilityPaymentList(
  //                 source: CommonScreens.NEW_DASHBOARD,
  //               ),
  //             ),
  //           );
  //         },
  //       ),
  //     ],
  //   ),
  // ),
  //           if (_isUpdateAvailable)
  //             GestureDetector(
  //               onTap: _performUpdate,
  //               child: Container(
  //                 height: 90,
  //                 margin: EdgeInsets.symmetric(vertical: 8),
  //                 padding: EdgeInsets.all(8),
  //                 decoration: BoxDecoration(
  //                   borderRadius: BorderRadius.circular(20),
  //                   boxShadow: [
  //                     BoxShadow(
  //                       color: Theme.of(context)
  //                           .colorScheme
  //                           .onSurface
  //                           .withOpacity(0.1),
  //                       blurRadius: 10,
  //                       offset: Offset(0, 6),
  //                     ),
  //                   ],
  //                 ),
  //                 child: ClipRRect(
  //                   borderRadius: BorderRadius.circular(15),
  //                   child: Stack(
  //                     alignment: Alignment.center,
  //                     children: [
  //                       BackdropFilter(
  //                         filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
  //                         child: Container(
  //                           decoration: BoxDecoration(
  //                             color: Theme.of(context)
  //                                 .colorScheme
  //                                 .surface
  //                                 .withOpacity(0.3),
  //                             borderRadius: BorderRadius.circular(15),
  //                           ),
  //                         ),
  //                       ),
  //                       Padding(
  //                         padding: const EdgeInsets.symmetric(horizontal: 12),
  //                         child: Row(
  //                           crossAxisAlignment: CrossAxisAlignment.center,
  //                           children: [
  //                             Expanded(
  //                               child: Column(
  //                                 crossAxisAlignment: CrossAxisAlignment.start,
  //                                 mainAxisAlignment: MainAxisAlignment.center,
  //                                 children: [
  //                                   Text(
  //                                     'New Update Available',
  //                                     style: TextStyle(
  //                                       color: Color(0xFF16c72e),
  //                                       fontWeight: FontWeight.w600,
  //                                       fontSize: 18,
  //                                       letterSpacing: -0.5,
  //                                       shadows: [
  //                                         Shadow(
  //                                           color: Theme.of(context)
  //                                               .colorScheme
  //                                               .onSurface
  //                                               .withOpacity(0.3),
  //                                           offset: Offset(0, 1),
  //                                           blurRadius: 1,
  //                                         ),
  //                                       ],
  //                                     ),
  //                                   ),
  //                                   SizedBox(height: 4),
  //                                   Container(
  //                                     padding: EdgeInsets.symmetric(
  //                                         horizontal: 8, vertical: 2),
  //                                     decoration: BoxDecoration(
  //                                       color:
  //                                           Colors.redAccent.withOpacity(0.15),
  //                                       borderRadius: BorderRadius.circular(8),
  //                                       border: Border.all(
  //                                         color:
  //                                             Colors.redAccent.withOpacity(0.3),
  //                                         width: 1,
  //                                       ),
  //                                     ),
  //                                     child: Row(
  //                                       mainAxisSize: MainAxisSize.min,
  //                                       children: [
  //                                         Container(
  //                                           width: 6,
  //                                           height: 6,
  //                                           decoration: BoxDecoration(
  //                                             color: Colors.redAccent,
  //                                             shape: BoxShape.circle,
  //                                           ),
  //                                         ),
  //                                         SizedBox(width: 6),
  //                                         Text(
  //                                           'High Priority',
  //                                           style: TextStyle(
  //                                             color: Colors.redAccent,
  //                                             fontWeight: FontWeight.w700,
  //                                             fontSize: 9,
  //                                             letterSpacing: 0.5,
  //                                           ),
  //                                         ),
  //                                       ],
  //                                     ),
  //                                   ),
  //                                 ],
  //                               ),
  //                             ),
  //                             Container(
  //                               height: 50,
  //                               width: 50,
  //                               decoration: BoxDecoration(
  //                                 borderRadius: BorderRadius.circular(10),
  //                                 border: Border.all(
  //                                   width: 0.3,
  //                                   color:
  //                                       Theme.of(context).colorScheme.onSurface,
  //                                 ),
  //                                 // gradient: LinearGradient(
  //                                 //   begin: Alignment.topLeft,
  //                                 //   end: Alignment.bottomRight,
  //                                 //   colors: [
  //                                 //     Colors.white.withOpacity(0.9),
  //                                 //     Colors.white.withOpacity(0.95),
  //                                 //   ],
  //                                 // ),
  //                               ),
  //                               child: Padding(
  //                                 padding: const EdgeInsets.all(5.0),
  //                                 child: Lottie.network(
  //                                   'https://fstech-cms-db.s3.ap-south-1.amazonaws.com/update_available_a45f819195.json',
  //                                   fit: BoxFit.contain,
  //                                 ),
  //                               ),
  //                             ),
  //                           ],
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           Text(
  //             'Household',
  //             style: Theme.of(context).textTheme.headlineMedium,
  //           ),
  //           SizedBox(height: 8),
  //           // OneSociety(), VizlogCard(), OneMeet(),
  //           ...reorderableItems,
  //           SizedBox(height: 20),
  //         ],
  //       );
  //     },
  //   );
  // }

  void _showUpdateContentBottomSheet(BuildContext context) async {
    Map<String, String> iosUpdateDetails = await fetchIOSUpdateDetails();

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            // Align text to the left
            children: [
              // Title
              Center(
                child: Text(
                  "Update Details",
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                ),
              ),
              SizedBox(height: 20),

              // Current Version
              Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blueGrey),
                  SizedBox(width: 10),
                  Text(
                    "Current Version: ",
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                  ),
                  Text(
                    // Theme.of(context).platform == TargetPlatform.android ?
                    iosUpdateDetails['currentVersion'] ?? 'Unknown',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                  ),
                ],
              ),

              SizedBox(height: 10),

              // Latest Version
              Row(
                children: [
                  Icon(Icons.system_update, color: Colors.green),
                  SizedBox(width: 10),
                  Text(
                    "Latest Version: ",
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                  ),
                  Text(
                    iosUpdateDetails['latestVersion'] ?? 'Unknown',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                  ),
                ],
              ),

              SizedBox(height: 20),

              // Release Notes (with a divider)
              Divider(color: Colors.grey.shade300),
              Text(
                "What's New",
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
              ),
              SizedBox(height: 10),
              Text(
                iosUpdateDetails['releaseNotes'] ??
                    'No release notes available.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
              ),

              SizedBox(height: 30),
              CustomLargeBtn(onPressed: _performUpdate, text: "Update Now"),
            ],
          ),
        );
      },
    );
  }

  Future<String> fetchAndroidUpdateNotes() async {
    try {
      final apiKey =
          'd3a20a369e139c778a10145ab30123dc61cac3d6e07a5776108d793d115d50bc'; // Replace with your SerpAPI key
      final url =
          'https://serpapi.com/search.json?engine=google_play&store=apps&q=com.cubeone.app&api_key=$apiKey';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        var data = json.decode(response.body);

        if (data['organic_results'] != null &&
            data['organic_results'].isNotEmpty) {
          var productDetailsLink =
              data['organic_results'][0]['items'][0]['serpapi_link'];

          final productDetailsUrl = '$productDetailsLink&api_key=$apiKey';

          final productResponse = await http.get(Uri.parse(productDetailsUrl));
          print("productResponse ${productResponse.statusCode}");

          if (productResponse.statusCode == 200) {
            var productData = json.decode(productResponse.body);
            print("ProductData $productData");

            // Access product information (if exists)
            var productInfo = productData['product_info'];
            print("productInfo $productInfo");
            var title = productInfo['title'] ?? 'No title available';
            var author = productInfo['authors'] != null
                ? productInfo['authors'][0]['name']
                : 'No author available';
            var downloads =
                productInfo['downloads'] ?? 'No download info available';

            // Print the extracted product info
            print("Title: $title");
            print("Author: $author");
            print("Downloads: $downloads");

            return "Title: $title\nAuthor: $author\nDownloads: $downloads";
          } else {
            return "Failed to fetch detailed app information.";
          }
        } else {
          return "No app details found.";
        }
      } else {
        return "Failed to fetch app details. Status code: ${response.statusCode}";
      }
    } catch (e) {
      print('Error fetching Android update details: $e');
      return "Error fetching update details.";
    }
  }

  Future<Map<String, String>> fetchIOSUpdateDetails() async {
    try {
      // Get the current version installed on the user's device
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      String currentVersion = packageInfo.version;

      // Fetch the latest version and release notes from the iTunes API
      final response = await http.get(
        Uri.parse('https://itunes.apple.com/lookup?bundleId=com.cubeone.app'),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);

        if (data['resultCount'] > 0) {
          String latestVersion = data['results'][0]['version'] ?? 'Unknown';
          String releaseNotes = data['results'][0]['releaseNotes'] ??
              'No release notes available.';

          // Return a map containing the current version, latest version, and release notes
          return {
            'currentVersion': currentVersion,
            'latestVersion': latestVersion,
            'releaseNotes': releaseNotes,
          };
        } else {
          throw Exception('No results found in the iTunes response');
        }
      } else {
        throw Exception('Failed to fetch iOS update details');
      }
    } catch (e) {
      print("Error fetching iOS update details: $e");
      return {
        'currentVersion': 'Unknown',
        'latestVersion': 'Unknown',
        'releaseNotes': 'Error fetching update details.',
      };
    }
  }

  // Widget _buildAppBar(BuildContext context) {
  //   return SliverAppBar(
  //       automaticallyImplyLeading: false,
  //       titleSpacing: 10,
  //       title: _buildProfileContent(context),
  //       toolbarHeight: 65,
  //       actions: _buildAppBarActionButtons(context));
  // }

  Widget _buildProfileContent(BuildContext context) {
    return InkWell(
      onTap: () {
        _openProfile();
      },
      child: FutureBuilder<dynamic>(
          future: AppUtils.getSsoProfile(),
          builder: (BuildContext context, AsyncSnapshot<dynamic> snapshot) {
            if (snapshot.hasData) {
              var profileObj = snapshot.data;

              return Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // _buildProfileImage(context, profileObj),
                    _buildProfileTexts(context, profileObj),
                  ],
                ),
              );
            } else {
              return Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  WidgetUtils.getCircularPageLoaderWidget(
                      strokeWidth: 3, size: Size(24, 24)),
                ],
              );
            }
          }),
    );
  }

  // Widget _buildProfileImage(BuildContext context, dynamic profileObj) {
  //   String? profileImageUrl = _getProfileUrl(profileObj);

  //   return Container(
  //     margin: EdgeInsets.symmetric(vertical: 4, horizontal: 4),
  //     decoration: BoxDecoration(
  //       borderRadius: BorderRadius.circular(12),
  //       border: Border.all(width: 1, color: Colors.white),
  //       boxShadow: [
  //         BoxShadow(
  //           color: Theme.of(context).shadowColor.withOpacity(0.15),
  //           blurRadius: 5.0, // soften the shadow
  //           spreadRadius: 1.0, //extend the shadow
  //           offset: Offset(1.0, 1.0),
  //         )
  //       ],
  //     ),
  //     child: ClipRRect(
  //       borderRadius: BorderRadius.circular(10),
  //       child: ValidationUtils.isUrlValid(profileImageUrl)
  //           ? CachedNetworkImage(
  //               width: 42,
  //               height: 42,
  //               fit: BoxFit.cover,
  //               imageUrl: profileImageUrl!,
  //               placeholder: (context, url) => Image.asset(
  //                 'assets/new_dashboard/images/loading.gif',
  //                 fit: BoxFit.cover,
  //               ),
  //               errorWidget: (context, url, error) =>
  //                   WidgetUtils.getDefaultProfilePicWidget(context,
  //                       size: Size(42, 42)),
  //             )
  //           : WidgetUtils.getDefaultProfilePicWidget(context,
  //               size: Size(42, 42)),
  //     ),
  //   );
  // }

  Widget _buildProfileTexts(BuildContext context, dynamic profileObj) {
    String? name = AppUtils.getSsoProfileName1(profileObj);

    return Expanded(
      child: Container(
        padding: const EdgeInsets.only(left: 6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Text(
              'Welcome'.toLowerCase(),
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .merge(TextStyle(letterSpacing: 1.5)),
            ),
            Text(
              name != null ? name.trim() : "",
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.titleSmall!.merge(TextStyle(
                  color: Theme.of(context).secondaryHeaderColor,
                  letterSpacing: 1.5,
                  fontWeight: FontWeight.w700)),
            ),
          ],
        ),
      ),
    );
  }

  // List<Widget> _buildAppBarActionButtons(BuildContext context) {
  //   return <Widget>[
  //     //_buildScanQrActionButton(context),
  //     _buildNotificationActionButton(context),
  //     //_buildCartActionButton(context),
  //     _buildDashboardSwitchActionButton(context)
  //   ];
  // }

  // Widget _buildScanQrActionButton(BuildContext context) {
  //   return Container(
  //     width: 36,
  //     height: 36,
  //     decoration: BoxDecoration(
  //       borderRadius: BorderRadius.circular(12),
  //       color: Colors.transparent,
  //     ),
  //     margin: EdgeInsets.symmetric(horizontal: 3, vertical: 10),
  //     child: IconButton(
  //       icon: Icon(Icons.document_scanner_outlined,
  //           color: Theme.of(context).primaryColor, size: 24),
  //       onPressed: () {},
  //     ),
  //   );
  // }

  Widget _buildNotificationActionButton(BuildContext context) {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.transparent,
      ),
      margin: EdgeInsets.symmetric(horizontal: 3, vertical: 10),
      child: IconButton(
        icon: Icon(Icons.notifications_none_outlined,
            color: Theme.of(context).primaryColor, size: 24),
        onPressed: () {
          _openNotification();
        },
      ),
    );
  }

  // Widget _buildDashboardSwitchActionButton(BuildContext context) {
  //   return Container(
  //     width: 36,
  //     height: 36,
  //     decoration: BoxDecoration(
  //       borderRadius: BorderRadius.circular(12),
  //       color: Colors.transparent,
  //     ),
  //     margin: EdgeInsets.symmetric(horizontal: 3, vertical: 10),
  //     child: IconButton(
  //       icon: Icon(Icons.switch_left,
  //           color: Theme.of(context).primaryColor, size: 24),
  //       onPressed: () {
  //         _openLegacyDashboard();
  //       },
  //     ),
  //   );
  // }

  // Widget _buildBody(BuildContext context) {
  //   return Container(
  //     alignment: Alignment.center,
  //     child: _buildNavigationContent(context),
  //   );
  // }

  // Widget _buildNavigationContent(BuildContext context) {
  //   return Container(
  //       margin: const EdgeInsets.only(top: 16),
  //       child: TabBarView(
  //         physics: NeverScrollableScrollPhysics(),
  //         controller: _tabController ?? TabController(length: 3, vsync: this),
  //         children: <Widget>[
  //           HouseholdContent(),
  //           MarketContent(),
  //           WorkContent(),
  //         ],
  //       ));
  // }

  Widget buildMyBusinessCard() {
    return isBusinessMode ? MyBusinessCard() : Container();
  }

  void _loadBusinessMode() {
    try {
      UserUtils.getHRMSCompanies().then((hrmsCompanies) {
        hrmsCompanies == null || hrmsCompanies.length <= 0
            ? isBusinessMode = false
            : isBusinessMode = true;

        setState(() {});
      });
    } catch (e) {
      print(e);
    }
  }

  void _initNewUpdateDialogVisibility() async {
    try {
      final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;

      // Fetch remote config values
      await remoteConfig.fetchAndActivate();

      // Get the update JSON from Remote Config
      String newUpdateJson = remoteConfig.getString('your_remote_key');

      // Log the fetched JSON
      print("Fetched Update JSON: $newUpdateJson");

      // Handle version comparison and visibility
      int currentVersionCode = await AppUtils.getAppVersionCode();
      int lastRejectedUpdateVersionCode =
          await SsoStorage.getLastRejectedUpdateVersionCode();

      if (lastRejectedUpdateVersionCode > 0 &&
          lastRejectedUpdateVersionCode <= currentVersionCode) {
        SsoStorage.setLastRejectedUpdateVersionCode(-1);
      }

      // Pass newUpdateJson to the NewUpdateHelper to check for updates
      if (newUpdateJson.isNotEmpty) {
        NewUpdateHelper().checkAndShowNewUpdateAvailability(context,
            onPressedUpdateNow: () {
          AppUtils.openAppStore();
        }, onPressedUpdateLater: () {
          // Define any action for 'Update Later'
        });
      }
    } catch (e) {
      print("Error initializing update dialog visibility: $e");
    }
  }

  void _checkNewUpdateAvailability(BuildContext context) async {
    NewUpdateHelper().checkAndShowNewUpdateAvailability(context,
        onPressedUpdateNow: () => AppUtils.openAppStore());
  }

  Future<Null> _passNodeUrl() async {
    if (Environment().getCurrentConfig().geCurrentPlatForm() ==
        FsPlatforms.ANDROID) {
      await platform.invokeMethod('node_url', {
        "node_url_key": Environment().getCurrentConfig().nodeUrl.toString()
      });
    } else {
      print("Not implemented node features");
    }
  }

  void shareApp(BuildContext context) {
    FsShare().myShare(context, FsString.SHARE_APP_URL);
  }

  Future<void> initPlatformState() async {
    // String? deeplinkUrl;

    // Check if the app was opened from a Facebook deep link
    /*var appLinkData = await FlutterFacebookAppLinks.getDeepLink();
    if (appLinkData != null) {
      // Get the deep link URL from the Facebook App Link
      print('Deep link URL: $appLinkData');
    }

    // Call your redirection function with the deep link URL
    if (!mounted) return;
    onRedirected(appLinkData);*/
  }

  Future<String?>? getSharedText() async {
    if (Environment().getCurrentConfig().geCurrentPlatForm() ==
        FsPlatforms.ANDROID) {
      final sharedData = await platform_channel.invokeMethod("getSharedText");
      if (sharedData != null) onRedirected(sharedData);
    } else {
      print("Not implemented node features");
    }
    return null;
  }

  Future<void> handleLinkData(dynamic type, Map map, String? link) async {
    if (!mounted) return;
    try {
      if (link != null &&
          link.trim().contains(Environment.config.meetingDomain)) {
        print("OPEN MEETING URI = $link");
        String? meetingIdStr = MeetVoteUtils.getMeetingIdFromUrl(link);
        if (meetingIdStr != null && meetingIdStr.trim().isNotEmpty) {
          _openMeetingDetailsScreen(meetingIdStr);
        }
        return;
      }

      if (map["action"] == "onegate-guest-invite") {
        print("OPEN GUEST INVITATION URI = $link");
        if (map.isNotEmpty) {
          NewGuestInvitationActionDelegate _guestInvitationActionDelegate =
              NewGuestInvitationActionDelegate();
          _guestInvitationActionDelegate.handleNewGuestInvitationAction(
              context, map);
        }
        return;
      }

      print(map);

      if (link != null && link.trim().contains(Environment.config.gateDomain)) {
        if (link
            .trim()
            .contains(MessagingConstants.ACTION_NOTIFICATION_LINK_ENDPOINT)) {
          if (map.isNotEmpty) {
            MessagingNotificationActionDelegate()
                .handleReceivedIntercomMessageNotification(context, map, type);
          }
        }
        return;
      }

      if ((map["u"] != "" && map["s"] != "")) {
        StoreDetailHelper(context).handleDynamicLink(map, link);
      }

      if (widget.isDirectToNotification) {
        if (map["card"] != null) {
          if (PaymentNotificationConstants.all
              .contains(map["card"].toLowerCase())) {
            PaymentNotificationActionDelegate()
                .handlePaymentNotification(context, map);
          } else if (MeetingNotificationConstants.all
              .contains(map["card"].toLowerCase())) {
            MeetingNotificationActionDelegate()
                .handleMeetingNotification(context, map);
          } else if (ApartmentNotificationConstants.all
              .contains(map["card"].toLowerCase())) {
            ApartmentNotificationActionDelegate()
                .handleApartmentNotification(context, map);
          } else if (VizlogNotificationConstants.all
              .contains(map["card"].toLowerCase())) {
            VizlogNotificationActionDelegate()
                .handleVizlogNotification(context, map);
          } else if (PynaNotificationConstants.all
              .contains(map["card"].toLowerCase())) {
            PynaNotificationActionDelegate()
                .handlePynaNotification(context, map);
          } else if (map["card"] == "ipl") {
            Map value = await (SsoStorage.getUserProfile()
                as FutureOr<Map<dynamic, dynamic>>);
            List<UserProfile> list = [];
            //list.add(userprofile);
            list.add(UserProfile(
              email: value['email'],
              firstname: value['first_name'],
              lastname: value['last_name'],
              mobile: value['mobile'],
              userid: value['user_id'],
            ));
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => IplMain(
                  userprofile: list,
                ),
              ),
            );
            /*SsoStorage.getUserProfile().then((userprofile) {

                });*/
            //SocietyCard.staticGlobalKey.currentState.societyIntroduction();
          } else if (map["card"] == "diwali") {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DiwaliMain(
                  userprofile: null,
                ),
              ),
            );
          } else {
            StoreDetailHelper(context).handleDynamicLink(map, link);
          }
        } else {
          StoreDetailHelper(context).handleDynamicLink(map, link);
        }

        // StoreDetailHelper(context).handleDynamicLink(map);
      } else {
        widget.isDirectToNotification = true;
      }
    } catch (e) {
      print(e);
    }
  }

  void onRedirected(String? uri, {String? messageType}) {
    if (!mounted) return;
    try {
      Map map = Uri.parse(uri!).queryParameters;
      handleLinkData(messageType, map, uri);
    } catch (e) {}
  }

  void _openMeetingDetailsScreen(String meetingId) {
    print("Meeting ID - $meetingId");
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MeetVoteMain(meetingId: meetingId),
      ),
    );
  }

  void catchFBDeferredDeeplinks() async {
    try {
      /* Map<String, String>? data = await (FlutterFacebookAppLinks.initFBLinks()
          as FutureOr<Map<String, String>?>);
      print("catchFBDeferredDeeplinks");
      print(data);
      if (data != null &&
          data['deeplink'] != null &&
          data['deeplink']!.isNotEmpty) {
        onRedirected(data['deeplink']);
        */ /*Map map = Uri.parse(data['deeplink']).queryParameters;
        if (map['c_id'] != null) {
          print(map);
          StoreDetailHelper(context).handleDynamicLink(map);
        }*/ /*

        /// do stuffs with the deeplink
      }

      if (data != null &&
          data['promotionalCode'] != null &&
          data['promotionalCode']!.isNotEmpty) {
        /// do stuffs with the promo code
      }*/
    } catch (e) {
      print('Error on FB APP LINKS');
    }
  }

  void _openLegacyDashboard() {
    DashboardHelper.getInstance().setNewDashboardDefault(false);
    DashboardHelper.getInstance().openDashboard(context);
  }

  void _openProfile() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (BuildContext context) {
          return ProfileView(
            source: CommonScreens.NEW_DASHBOARD,
            notifyFunction: () {
              setState(() {});
            },
          );
        },
      ),
    );
  }

  Future<void> _openNotification() async {
    await FsNavigator.push(context, MyFlatsInvitees());
  }

  void _loadNotificationCount() {
    MemberInvitationPresenter invitationPresenter =
        MemberInvitationPresenter(this);
    invitationPresenter.getMemberInvitation();
  }

  String? _getProfileUrl(dynamic profileObj) {
    if (profileObj == null) {
      return null;
    }

    String? p = profileObj["avatar_small"];
    return (p != null && p.trim().isNotEmpty)
        ? (p + "?dummy=${counter++}")
        : null;
  }

  @override
  void onRespondSuccess(success) {}

  @override
  void onError(error) {
    notifications = 0;
    setState(() {});
  }

  @override
  void onFailure(failure) {
    notifications = 0;
    setState(() {});
  }

  @override
  void onSuccess(success) {
    if (success != null)
      notifications = success.length;
    else
      notifications = 0;
    setState(() {});
  }

  @override
  error(error, {callingType}) {}

  @override
  failure(failed, {callingType}) {}

  @override
  success(success, {callingType, String? searchedText}) {
    if (callingType == "getNotification") {
      if (success != null) {
        List? items = [];
        items = success['data']['results'];
        notificationList.clear();
        for (int i = 0; i < items!.length; i++) {
          if (items[i]['status'] == 'unread') {
            notificationList.add(items[i]);
          }
        }
        notificationCount = notificationList.length;
      }
    }
    setState(() {});
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}

class PrimaryShortcut extends StatefulWidget {
  final String title;
  final String imageUrl;
  final Widget? widget;
  final Color? titleColor;
  DashboardCardController? controller;
  Widget? contentLoader;
  final void Function() onTap;

  PrimaryShortcut({
    super.key,
    required this.title,
    required this.imageUrl,
    this.widget,
    this.titleColor,
    this.controller,
    required this.onTap,
  });

  @override
  State<PrimaryShortcut> createState() => _PrimaryShortcutState();
}

class _PrimaryShortcutState extends State<PrimaryShortcut> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        clipBehavior: Clip.hardEdge,
        // padding: EdgeInsets.all(10),
        height: 180,
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          // color: Colors.black,
          borderRadius: BorderRadius.circular(10.0),
          // backgroundBlendMode: BlendMode.darken,
          // image: DecorationImage(
          //   colorFilter: ColorFilter.mode(
          //     Colors.black.withOpacity(
          //       0.4,
          //     ),
          //     BlendMode.dstATop,
          //   ),
          //   image: CachedNetworkImageProvider(
          //     "http://via.placeholder.com/350x150",
          //   ),
          //   // image: AssetImage(
          //   //   // 'assets/new_assets/images/meetNew.png',
          //   //   imageUrl,
          //   // ),
          //   fit: BoxFit.cover,
          // ),
        ),
        child: Stack(
          children: [
            // CachedNetworkImage(
            //   imageUrl: "http://via.placeholder.com/350x150",
            //   fit: BoxFit.cover,
            //   placeholder: (context, url) => CircularProgressIndicator(),
            //   errorWidget: (context, url, error) => Icon(Icons.error),
            // ),
            ClipRRect(
              borderRadius: BorderRadius.circular(10.0),
              child: Image.asset(
                widget.imageUrl,
                fit: BoxFit.cover,
                height: 195,
                width: MediaQuery.of(context).size.width,
              ),
            ),
            Container(
              padding: EdgeInsets.all(10),
              // padding: ,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.4),
                borderRadius: BorderRadius.circular(10.0),
                backgroundBlendMode: BlendMode.darken,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    child: RichText(
                      text: TextSpan(
                        style:
                            Theme.of(context).textTheme.displaySmall!.copyWith(
                                  color: widget.titleColor ??
                                      Theme.of(context).colorScheme.surface,
                                ),
                        children: [
                          TextSpan(
                            text: 'one',
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          ),
                          TextSpan(
                            text: widget.title,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                        height: 50,
                        width: 50,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(
                            width: 1,
                            color: Theme.of(context).colorScheme.surface,
                          ),
                          borderRadius: BorderRadius.circular(50),
                        ),
                        child: Icon(
                          Icons.north_east_outlined,
                          color: kPrimaryBlackColor,
                        ),
                      ),
                      widget.controller?.showContentLoading == true
                          ? _buildContentLoader(context)
                          : widget.widget ?? SizedBox(),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentLoader(BuildContext context) {
    return widget.contentLoader != null
        ? widget.contentLoader!
        : SizedBox.shrink();
  }
}

class HomeQuickPay extends StatelessWidget {
  const HomeQuickPay({
    required this.text,
    this.icon,
    required this.onTap,
    this.hasBharatConnectLogo = false,
  });

  final String text;
  final IconData? icon;
  final void Function() onTap;
  final bool hasBharatConnectLogo;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.only(top: 10, bottom: 10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                width: 0.3,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            height: 50,
            width: 50,
            child: hasBharatConnectLogo
                ? CachedNetworkImage(
                    imageUrl:
                        "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/Bharat_Connect_Transparent_Logo_PNG_transparent_bg.png",
                    placeholder: (context, url) => SizedBox(
                      height: 35,
                      child: Center(
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      'assets/images/bbps_logo.png',
                      height: 35,
                      fit: BoxFit.contain,
                    ),
                    height: 35,
                    fit: BoxFit.contain,
                  )
                : Icon(
                    icon,
                    size: 30,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
          ),
          Text(
            text,
            style: Theme.of(context).textTheme.labelLarge,
          ),
        ],
      ),
    );
  }
}

class MyCarousel extends StatelessWidget {
  final List<Map<String, dynamic>> jsonData;

  MyCarousel({required this.jsonData});

  @override
  Widget build(BuildContext context) {
    final deviceSize = MediaQuery.of(context).size;

    return ExpandableCarousel(
      options: ExpandableCarouselOptions(
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        enableInfiniteScroll: true,
        viewportFraction: 1,
        showIndicator: false,
        padEnds: false,
      ),
      items: jsonData.asMap().entries.map((entry) {
        final data = entry.value;
        return Builder(
          builder: (BuildContext context) {
            return GestureDetector(
              onTap: () {
                // navigateToDetailPage(context, data);
              },
              child: Container(
                height: deviceSize.height * 0.3,
                width: deviceSize.width,
                decoration: BoxDecoration(
                  color: Colors.black,
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.all(
                        Radius.circular(10.0),
                      ),
                      child: ColorFiltered(
                        colorFilter: ColorFilter.mode(
                          Colors.black.withOpacity(0.6),
                          BlendMode.dstATop,
                        ),
                        child: Image.asset(
                          data['thumbnail_img'],
                          fit: BoxFit.cover,
                          width: deviceSize.width,
                          height: deviceSize.height * 0.3,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 5.0,
                        bottom: 15,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ListTile(
                            title: Text(
                              data['title_text'],
                              style: Theme.of(context)
                                  .textTheme
                                  .displayMedium!
                                  .copyWith(
                                    color: Colors.white,
                                  ),
                            ),
                            subtitle: Text(
                              data['subtitle_text'],
                              textAlign: TextAlign.start,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge!
                                  .copyWith(
                                    color: Colors.white,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }).toList(),
    );
  }

  void navigateToDetailPage(BuildContext context, Map<String, dynamic> data) {
    print("Navigate to detail page: ${data['title_text']}");
  }
}
