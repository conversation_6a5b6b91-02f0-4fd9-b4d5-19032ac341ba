import 'package:confetti/confetti.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/test_widgets/sonic_branding_comprehensive_test.dart';
import 'package:sso_futurescape/ui/module/dashboard/new/ui/app_dashboard.dart';
import 'package:sso_futurescape/ui/module/sso/profile/profile_view.dart';
import 'package:sso_futurescape/utils/base_loader_utils.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';

class MainDashboardV2 extends StatefulWidget {
  final int? currentIndex;

  MainDashboardV2({Key? key, this.currentIndex}) : super(key: key);

  @override
  State<MainDashboardV2> createState() => _MainDashboardV2State();
}

class _MainDashboardV2State extends State<MainDashboardV2> {
  late ConfettiController _controllerCenter;
  late ScrollController _scrollController;
  bool _isAppBarExpanded = true;
  ImageProvider<Object>? _profileImage; // Stores the profile image
  bool _isLoading = true; // Loading indicator

  @override
  void initState() {
    super.initState();
    _controllerCenter =
        ConfettiController(duration: const Duration(seconds: 10));
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);

    _loadProfileImage();
  }

  @override
  void dispose() {
    _controllerCenter.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    bool isExpanded =
        _scrollController.hasClients && _scrollController.offset < 180;
    if (isExpanded != _isAppBarExpanded) {
      setState(() {
        _isAppBarExpanded = isExpanded;
      });
    }
  }

  Future<void> _loadProfileImage() async {
    try {
      var userProfile = await SsoStorage.getUserProfile();
      // print("this is profile$userProfile");
      String? profileImageURL = userProfile?['avatar_large'];

      ImageProvider<Object> imageProvider =
          (profileImageURL != null && profileImageURL.isNotEmpty)
              ? NetworkImage(profileImageURL)
              : const AssetImage("images/default.png");

      setState(() {
        _profileImage = imageProvider;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _profileImage = const AssetImage("images/default.png");
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        final shouldPop = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title:
                Text('Exit', style: Theme.of(context).textTheme.headlineMedium),
            content: Text('Do you really want to exit?',
                style: Theme.of(context).textTheme.bodySmall),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('No', style: Theme.of(context).textTheme.bodySmall),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child:
                    Text('Yes', style: Theme.of(context).textTheme.bodyLarge),
              ),
            ],
          ),
        );

        if (shouldPop ?? false) {
          SystemNavigator.pop();
        }
        return Future.value(false);
      },
      canPop: false,
      child: MyScrollView(
        controller: _scrollController,
        hasBackButton: false,
        hasLogoPageTitle: true,
        bodyHorizontalPadding: 0,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarIconBrightness:
              _isAppBarExpanded ? Brightness.light : Brightness.dark,
          statusBarBrightness:
              _isAppBarExpanded ? Brightness.light : Brightness.dark,
        ),
        actions: [
          // Debug button for sonic branding test (only in debug mode)
          if (kDebugMode)
            IconButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SonicBrandingComprehensiveTest(),
                  ),
                );
              },
              icon: Icon(Icons.music_note, color: Colors.white),
              tooltip: 'Test Sonic Branding',
            ),
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ProfileView(
                    source: CommonScreens.NEW_DASHBOARD,
                    notifyFunction: () {},
                  ),
                ),
              );
            },
            child: CircleAvatar(
              radius: 20,
              backgroundColor: Colors.white,
              child: _isLoading
                  ? const CircularProgressIndicator() // Show loader while fetching
                  : CircleAvatar(
                      radius: 20,
                      backgroundImage: _profileImage,
                    ),
            ),
          ),
        ],
        flexibleSpaceBar: SizedBox(
          child: FutureBuilder(
            future: DataService().loadData(),
            builder:
                (context, AsyncSnapshot<List<Map<String, dynamic>>> snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return CircularProgressIndicator();
              } else if (snapshot.hasError) {
                return Container(
                  height: MediaQuery.of(context).size.height * 0.2,
                  child: Center(child: Text('Error: ${snapshot.error}')),
                );
              } else {
                return MyCarousel(jsonData: snapshot.data!);
              }
            },
          ),
        ),
        pageBody: HomeTab(),
      ),
    );
  }
}

class MySearchDelegate extends SearchDelegate {
  @override
  List<Widget>? buildActions(BuildContext context) {
    return [
      IconButton(
        icon: Icon(
          Symbols.clear,
          color: Theme.of(context).colorScheme.onSurface,
        ),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget? buildLeading(BuildContext context) {
    return IconButton(
      style: IconButton.styleFrom(
        backgroundColor:
            Theme.of(context).colorScheme.onSurface.withOpacity(0.03),
      ),
      // color: Colors.red,
      icon: Icon(
        Symbols.arrow_back,
        color: Theme.of(context).colorScheme.onSurface,
      ),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return Center(
      child: Text('You searched for "$query"'),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    List<String> suggestions = [
      'Ask OSCAR AI',
      'Create a meeting for later ',
      'Invite a guest',
      'Pay your maintenance bill',
    ];
    List<String> filteredSuggestions = suggestions
        .where((suggestion) =>
            suggestion.toLowerCase().contains(query.toLowerCase()))
        .toList();

    return ListView.builder(
      itemCount: filteredSuggestions.length,
      itemBuilder: (context, index) {
        return ListTile(
          title: Text(filteredSuggestions[index]),
          onTap: () {
            query = filteredSuggestions[index];
            showResults(context);
          },
        );
      },
    );
  }
}
