import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:common_config/utils/fs_navigator.dart';
import 'package:common_config/utils/toast/toast.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter/material.dart';

import 'package:material_symbols_icons/symbols.dart';
import 'package:pointycastle/asymmetric/api.dart';
//import 'package:pointycastle/pointycastle.dart';
import 'package:pointycastle/export.dart' as pce;
import 'package:sso_futurescape/config/colors/color.dart';
import 'package:sso_futurescape/config/environment/environment.dart';
import 'package:sso_futurescape/config/strings/strings.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';
import 'package:sso_futurescape/custom_widgets/bharat_connect_logo_widget.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_presenter.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_view.dart';
import 'package:sso_futurescape/ui/base/loading_constants.dart';
import 'package:sso_futurescape/ui/module/dashboard/new/helper/dashboard_helper.dart';
import 'package:sso_futurescape/ui/module/dashboard/new/ui/dashboard/main_dashboard.dart';
import 'package:sso_futurescape/ui/module/meeting/utils/grocery_ui_utils.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/electricity_billers_list.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/maintenance_payment/owner_details.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/payment_history.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/rent_payment/user_details.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/view_biller_details.dart';
import 'package:sso_futurescape/utils/app_constant.dart';
import 'package:sso_futurescape/utils/app_utils.dart';
import 'package:sso_futurescape/utils/base_loader_utils.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';
//import 'package:pointycastle/asymmetric/api.dart';
import 'package:sso_futurescape/utils/ui/loading_error_utils.dart';
import 'package:sso_futurescape/utils/widget_utils.dart';

class UilityPaymentList extends StatefulWidget {
  bool? isFromDashboard;
  int source;

  UilityPaymentList(
      {this.isFromDashboard, this.source = CommonScreens.APP_DASHBOARD});

  @override
  _UilityPaymentListState createState() => _UilityPaymentListState();
}

class _UilityPaymentListState extends State<UilityPaymentList>
    implements MobikwikView {
  final TextEditingController _searchControl = new TextEditingController();
  final paymentlist = Environment().getCurrentConfig().mobikwikUtilities;
  var encryptedId = "";
  late MobikwikPresenter mobikwikPresenter;
  bool _loadError = false;
  LoadingErrorType? _loadErrorType;
  String? _loadErrorMsg;
  int page = 1;
  bool isExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadPaymentOptions();
  }

  splitStr(String str) {
    var begin = '-----BEGIN PUBLIC KEY-----\n';
    var end = '\n-----END PUBLIC KEY-----';
    int splitCount = str.length ~/ 64;
    List<String> strList = [];

    for (int i = 0; i < splitCount; i++) {
      strList.add(str.substring(64 * i, 64 * (i + 1)));
    }
    if (str.length % 64 != 0) {
      strList.add(str.substring(64 * splitCount));
    }

    return begin + strList.join('\n') + end;
  }

  String encrypt(String text, pce.RSAPublicKey pubKey) {
    var cipher = pce.PKCS1Encoding(pce.RSAEngine());
    cipher.init(true, pce.PublicKeyParameter<pce.RSAPublicKey>(pubKey));
    Uint8List output1 = cipher.process(utf8.encode(text));
    return base64Encode(output1);
  }

  void openPaymentHistory() {
    FsNavigator.push(context, PaymentHistory());
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    var maxWidth = MediaQuery.of(context).size.width / 2;
    var width = MediaQuery.of(context).size.width;
    var columns = (width ~/ maxWidth) + 1;
    var columnWidth = width / columns;
    var aspectRatio = columnWidth / 120;

    // Determine the crossAxisCount based on the screen size
    int crossAxisCount = 4; // Default for mobile
    if (size.width >= 600 && size.width < 1200) {
      crossAxisCount = 6; // For tablets
    } else if (size.width >= 1200) {
      crossAxisCount = 8; // For desktops
    }

    return MyScrollView(
      logoPageTitle: 'pay',
      hasLogoPageTitle: true,
      actions: [
        BharatConnectLogo(height: 35),
        SizedBox(width: 8),
        SecuredByBBPS(),
      ],
      pageBody: (_loadError == true)
          ? Container(color: Colors.white, child: _buildErrorWidget())
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ListTile(
                  contentPadding: EdgeInsets.fromLTRB(0, 0, 10, 0),
                  title: Text(
                    'Recharge & Pay Bills',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                        // fontWeight: FontWeight.w600,
                        ),
                  ),
                  // subtitle: Text(
                  //   'Powered by BBPS',
                  //   style: Theme.of(context).textTheme.bodySmall,
                  // ),
                  // trailing: CachedNetworkImage(
                  //   imageUrl:
                  //       "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/Bharat_Connect_Primary_Logo_PNG_5cb35a279e.png",
                  //   placeholder: (context, url) => CircularProgressIndicator(),
                  //   errorWidget: (context, url, error) => Icon(Icons.error),
                  //   height: 40,
                  // ),

                  // Image.asset(
                  //   'assets/images/bbps_logo.png',
                  //   height: 28,
                  //   width: 90,
                  //   fit: BoxFit.cover,
                  // ),
                ),
                Container(
                  padding: EdgeInsets.only(top: 10),
                  margin: EdgeInsets.only(
                    bottom: 0,
                    top: 0,
                  ),
                  child: GridView.count(
                    padding: EdgeInsets.zero,
                    crossAxisCount: crossAxisCount,
                    childAspectRatio: 0.9,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    children: List<Widget>.generate(
                        isExpanded ? paymentlist.length : 8, (index) {
                      Map place = paymentlist[index];

                      Widget child;
                      if (index == 7 && isExpanded != true) {
                        child = InkWell(
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            setState(() {
                              isExpanded = !isExpanded;
                            });
                          },
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(100),
                                    border: Border.all(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface)),
                                child: Icon(
                                  Symbols.grid_view,
                                  size: 30,
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                ),
                              ),
                              const SizedBox(height: 4.0),
                              Text(
                                'More',
                                softWrap: true,
                                textAlign: TextAlign.center,
                                style: Theme.of(context).textTheme.labelSmall,
                              ),
                            ],
                          ),
                        );
                      } else {
                        child = InkWell(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () {
                            String? mobikwikUrls =
                                place['url'] + "&id=" + encryptedId;
                            debugPrint(mobikwikUrls, wrapWidth: 1084);
                            print("onepay page: ${place['name']}");
                            openPage(place['name']);
                          },
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(100),
                                    border: Border.all(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface)),
                                child:
                                    // index == 2
                                    place["img"] is String
                                        ? CachedNetworkImage(
                                            imageUrl: place["img"],
                                            placeholder: (context, url) =>
                                                CircularProgressIndicator(),
                                            errorWidget:
                                                (context, url, error) =>
                                                    Icon(Icons.error),
                                            width: 28,
                                            height: 28,
                                          )
                                        : Icon(
                                            place["img"],
                                            size: 25,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurface,
                                          ),
                              ),
                              const SizedBox(height: 4.0),
                              Expanded(
                                child: Text(
                                  place["name"] == FsString.DIGITAL_VOUCHAR
                                      ? "Google Play"
                                      : place["name"] ==
                                              FsString.PREPAID_MOBILE_RECHARGE
                                          ? 'Prepaid'
                                          : place["name"] ==
                                                  FsString
                                                      .POSTPAID_MOBILE_RECHARGE
                                              ? "Postpaid"
                                              : place["name"] ==
                                                      FsString.GAS_BILL
                                                  ? "Piped Gas"
                                                  : '${place["name"]}',
                                  softWrap: true,
                                  textAlign: TextAlign.center,
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelSmall!
                                      .copyWith(
                                        fontSize: 12,
                                      ),
                                ),
                              ),
                              const SizedBox(height: 4.0),
                            ],
                          ),
                        );
                      }

                      return child;
                    }),
                  ),
                ),
                // SizedBox(
                //   height: 10,
                // ),
                !isLoading && recentRecharge!.isEmpty
                    ? Container()
                    : ListTile(
                        contentPadding: EdgeInsets.zero,
                        title: Text('Transaction History',
                            style: Theme.of(context).textTheme.headlineLarge),
                        trailing: Text(
                          'View All',
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                        onTap: () {
                          openPaymentHistory();
                        },
                      ),
                !isLoading && recentRecharge!.isEmpty
                    ? Container()
                    : Container(
                        // color: Colors.red,
                        // padding: EdgeInsets.symmetric(horizontal: 10),
                        margin: EdgeInsets.only(bottom: 10),
                        child: !isLoading
                            ? ListView.builder(
                                padding: EdgeInsets.only(bottom: 60),
                                primary: false,
                                physics: NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount: recentRecharge == null
                                    ? 0
                                    : min(5, recentRecharge!.length),
                                itemBuilder: (BuildContext context, int index) {
                                  Map recentTranData = recentRecharge![index];
                                  return GestureDetector(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        border: Border(
                                          bottom: BorderSide(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurface
                                                .withOpacity(
                                                    0.1), // Choose your color
                                            width:
                                                1.0, // Adjust the width as needed
                                          ),
                                        ),
                                      ),
                                      child: ListTile(
                                        onTap: () {
                                          viewBillApiCall(recentTranData);
                                        },
                                        contentPadding: EdgeInsets.zero,
                                        leading: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(100),
                                          child: CachedNetworkImage(
                                            height: 40,
                                            width: 40,
                                            fit: BoxFit.cover,
                                            imageUrl:
                                                'https://static.mobikwik.com/appdata/operator_icons/op' +
                                                    '${recentTranData["op"].toString()}' +
                                                    '.png',
                                            placeholder: (context, url) =>
                                                Image.asset(
                                              'assets/img/loading.gif',
                                              fit: BoxFit.cover,
                                            ),
                                            errorWidget:
                                                (context, url, error) =>
                                                    Icon(Icons.error),
                                          ),
                                        ),
                                        title: Text(
                                          // '${recentTranData["op_name"]}',
                                          '${recentTranData["cn"]}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .labelSmall,
                                        ),
                                        subtitle: Text(
                                          recentTranData['type'] ==
                                                      'Mobile recharges' ||
                                                  recentTranData['type'] ==
                                                      'DTH' ||
                                                  recentTranData['type'] ==
                                                      'Fastag'
                                              ? 'Last amount : ₹${recentTranData["amt"]}'
                                              : 'Bill amount : ₹${recentTranData["amt"]}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium,
                                        ),
                                        trailing: Container(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.all(
                                              Radius.circular(50),
                                            ),
                                            border: Border.all(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .onSurface
                                                  .withOpacity(0.2),
                                            ),
                                          ),
                                          padding: EdgeInsets.all(10),
                                          child: Icon(
                                            Icons.refresh,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurface
                                                .withOpacity(0.8),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                })
                            : Container(
                                height: 200,
                                child: _getShimmeringListLoaderWidget()),
                      )
              ],
            ),
    );
  }

  getProfileDetail() {
    mobikwikPresenter = new MobikwikPresenter(this);
    recentRechargeData();
    SsoStorage.getUserProfile().then((profile) {
      var user_name = '7447744458';

      encryptedId = encrypt(
          user_name,
          RSAKeyParser().parse(splitStr(FsString.mobikwikPublicKey))
              as RSAPublicKey);
      encryptedId = Uri.encodeComponent(encryptedId);
      //debugPrint(encryptedId, wrapWidth: 1084);
    });
    setState(() {});
  }

  Widget _buildErrorWidget() {
    return Center(
      child: WidgetUtils.getErrorWidget(
          module: AppConstant.ONE_PAY,
          errorMsg: _loadErrorMsg!,
          errorType: _loadErrorType,
          showErrorIcon: true,
          shouldRetry: LoadingErrorUtils.canRetry(_loadErrorType),
          onRetryPressed: () {
            _loadPaymentOptions();
          },
          retryButtonColor: FsColor.primarypayment),
    );
  }

  void _loadPaymentOptions() {
    AppUtils.checkInternetConnection().then((value) {
      if (value) {
        _loadError = false;
        _loadErrorType = null;
        _loadErrorMsg = null;
        getProfileDetail();
      } else {
        _loadError = true;
        _loadErrorMsg = FsString.ERROR_NO_INTERNET_RETRY;
        _loadErrorType = LoadingErrorType.INTERNET;
      }
      setState(() {});
    });
  }

  void openMaintenancePayment() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OnepayMaintenancepayStep1(),
      ),
    );
  }

  openRentPayment() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => OnepayRentpayStep1()),
    );
  }

  void backClick(context) {
    if (CommonScreens.APARTMENT_DASHBOARD == widget.source ||
        CommonScreens.ADS == widget.source ||
        CommonScreens.DYNAMIC_LINK == widget.source ||
        CommonScreens.NOTIFICATION == widget.source ||
        CommonScreens.NEW_DASHBOARD == widget.source) {
      Navigator.of(context).pop();
    } else {
      /*Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => MainDashboard()),
          (Route<dynamic> route) => false);*/

      DashboardHelper.getInstance().openDashboard(context);
    }
  }

  passbillData({bankName, cn, a1, a2, a3, cirId, emailId, viewBill}) {
    return {
      "cn": cn,
      "ad1": a1,
      "ad2": a2,
      "ad3": a3,
      "cirId": cirId,
      "bankName": bankName,
      "emailId": emailId,
      "viewBill": viewBill
    };
  }

  viewBillApiCall(place) {
    Map op_data = {
      "Operator Id": place['op'].toString(),
      "Operator Name": place['op_name'].toString(),
      "amount": place['amt'],
      "cn": place['cn'].toString(),
      "bankName": place["ad1"] != null ? place['ad1'] : "",
      "ad1": place["ad1"] != null ? place['ad1'] : "",
      "ad2": place["ad2"] != null ? place['ad2'] : "",
      "ad3": place["ad3"] != null ? place['ad3'] : "",
      'viewBill':
          place.containsKey('ViewBill') ? place['ViewBill'] : 'not required',
      "cirId": place.containsKey("cirId") ? place['cirId'] : "",
    };
    return Navigator.push(
      context,
      MaterialPageRoute(
          builder: (context) => ViewBillerDetails(
              selectedOption: place['type'],
              operatorDetail: op_data,
              amount: op_data['amount'],
              action: 'recharge amt',
              mobileNumber: place['cn'].toString(),
              billersDetail: passbillData(
                  bankName: op_data['bankName'],
                  emailId: op_data['bankName'],
                  cn: place['cn'].toString(),
                  a1: op_data['ad1'],
                  a2: op_data['ad2'],
                  a3: op_data['ad3'],
                  cirId: op_data['cirId'],
                  viewBill: op_data['viewBill']))),
    );
  }

  openPage(page) {
    if (page == FsString.RENT_PAYMENT) {
      openRentPayment();
    } else if (page == FsString.MAINTENANCE) {
      openMaintenancePayment();
    } else if (page == "Bill Pay") {
      print("onepay page: $page");
      Toastly.success(context, "Bharat Connect");
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => ElectricityBillersList(
                  recentRecharge: viewBillApiCall,
                  name: page,
                )),
      );
    }
  }

  recentRechargeData() {
    mobikwikPresenter.showRecentRecharge(
        callingType: "RecentRecharge", type: "");
  }

  Widget _getShimmeringListLoaderWidget() {
    return GroceryUiUtils.operatorsListLoader();
  }

  @override
  error(error, {callingType}) {
    if (callingType == "getRecentRecharge") {
      print("error::" + error.toString());
    }
  }

  @override
  failure(failed, {callingType}) {
    if (callingType == "getRecentRecharge") {
      print("failed::" + failed.toString());
    }
  }

  @override
  ordererror(error, {callingType}) {
    throw UnimplementedError();
  }

  @override
  orderfailure(failed, {callingType}) {
    throw UnimplementedError();
  }

  @override
  ordersuccess(success, {callingType, String? searchedText}) {
    throw UnimplementedError();
  }

  bool isLoading = true;
  List? recentRecharge = [];

  @override
  success(success, {callingType, String? searchedText}) {
    if (callingType == "RecentRecharge") {
      if (success['data'].isNotEmpty) {
        isLoading = false;
        recentRecharge = success['data'];
      } else {
        isLoading = false;
      }
    }

    print("getRecentRecharge::" + success.toString());

    setState(() {});
  }
}

class MobikwikWebView extends StatefulWidget {
  String mobikwikUrls;

  MobikwikWebView(this.mobikwikUrls);

  @override
  State<StatefulWidget> createState() {
    return MobikwikWebViewState(mobikwikUrls);
  }
}

class MobikwikWebViewState extends State<MobikwikWebView> {
  // final flutterWebviewPlugin = new FlutterWebviewPlugin(); // REMOVED: Package removed for optimization
  String mobikwikUrls;

  MobikwikWebViewState(this.mobikwikUrls);

  @override
  void initState() {
    super.initState();

    // flutterWebviewPlugin.onUrlChanged.listen((String url) { // REMOVED: Package removed for optimization
    //   print(url);
    //   if (url.contains("chsone.in/dashboard")) {
    //     /*Navigator.pushAndRemoveUntil(
    //         context,
    //         MaterialPageRoute(builder: (context) => MainDashboard()),
    //         (Route<dynamic> route) => false);*/

    //     DashboardHelper.getInstance().openDashboard(context);
    //   }
    //   /*http://devwebsite.chsone.in/payment/success http://devwebsite.chsone.in/payment/failed*/
    // });
  }

  @override
  Widget build(BuildContext context) {
    // REMOVED: WebviewScaffold - Package removed for optimization
    return Scaffold(
        appBar: AppBar(
            backgroundColor: FsColor.primarypayment,
            elevation: 0.0,
            title: new Text(
              'Recharge & Bill Payments',
              style: FSTextStyle.appbartextlight,
            ),
            leading: IconButton(
                style: IconButton.styleFrom(
                  backgroundColor:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.03),
                ),
                // color: Colors.red,
                icon: Icon(
                  Symbols.arrow_back,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                onPressed: () {
                  Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) => MainDashboardV2(currentIndex: 0),
                  ));
                })

            // BackButton1(
            //   lightIcon: true,
            // ),
            ),
        body: Center(
          child: Text(
            'Webview functionality temporarily disabled.\nPlease use the mobile app or website.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ));
  }
}
