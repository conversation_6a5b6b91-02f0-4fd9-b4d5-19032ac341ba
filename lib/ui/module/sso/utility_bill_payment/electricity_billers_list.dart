// ignore_for_file: must_be_immutable

import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:sso_futurescape/config/colors/color.dart';
import 'package:sso_futurescape/config/strings/strings.dart';
import 'package:sso_futurescape/custom_widgets/custom_form_widget.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_presenter.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_view.dart';
import 'package:sso_futurescape/ui/base/loading_constants.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/bill_payment_successful.dart';
import 'package:sso_futurescape/ui/module/meeting/utils/grocery_ui_utils.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/add_biller_consumer_number.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/view_biller_details.dart';
import 'package:sso_futurescape/utils/app_constant.dart';
import 'package:sso_futurescape/utils/app_utils.dart';
import 'package:sso_futurescape/utils/ui/loading_error_utils.dart';
import 'package:sso_futurescape/utils/widget_utils.dart';

import '../../../../custom_widgets/secured_by_bbps_widget.dart';
import '../../../../custom_widgets/bharat_connect_logo_widget.dart';

class ElectricityBillersList extends StatefulWidget {
  final Function? recentRecharge;
  final bool? from_dashboard;
  var name;

  ElectricityBillersList({
    Key? key,
    this.name,
    this.from_dashboard,
    this.recentRecharge,
  }) : super(key: key);

  @override
  _ElectricityBillersListState createState() => _ElectricityBillersListState();
}

class _ElectricityBillersListState extends State<ElectricityBillersList>
    implements MobikwikView {
  // Controllers
  final TextEditingController _searchControl = TextEditingController();

  // State Variables
  bool _loadError = false;
  LoadingErrorType? _loadErrorType;
  String? _loadErrorMsg;
  bool isLoading = true;
  bool isLoadingList = true;
  int? _groupValue = -1;

  // Data holders
  List? billersList = [];
  Map? billersDetail = {};
  Map? op_Detail = {};

  // Presenter
  late MobikwikPresenter mobikwikPresenter;

  // ======== LifeCycle Methods ===========
  @override
  void initState() {
    super.initState();
    _loadOperatorList();
    debugPrint(
        "ElectricityBillersList from_dashboard ${widget.from_dashboard}");
  }

  @override
  void dispose() {
    _searchControl.dispose();
    super.dispose();
  }

  // ======== UI Build ===========
  @override
  Widget build(BuildContext context) {
    return MyScrollView(
      isScrollable: false,
      actions: [
        BharatConnectLogo(height: 35),
        SizedBox(width: 8),
        SecuredByBBPS(), // BBPS security widget
      ],
      pageTitle: _getPageTitle(widget.name),
      // style: FSTextStyle.appbartextlight,
      pageBody: _loadError
          ? _buildErrorWidget()
          : SizedBox(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Search bar
                  if (widget.name != FsString.DIGITAL_VOUCHAR)
                    _buildSearchBar(context),

                  // Radio Buttons (for Datacard only)
                  if (widget.name == "datacard prepaid" ||
                      widget.name == "datacard postpaid" ||
                      widget.name == "datacard")
                    SizedBox(height: 15),
                  if (widget.name == "datacard prepaid" ||
                      widget.name == "datacard postpaid" ||
                      widget.name == "datacard")
                    _buildRadioButtonSwitcher(context),

                  SizedBox(height: 15),
                  if (!isLoading && billersList!.isEmpty)
                    Container()
                  else
                    _buildMainTitle(context),

                  SizedBox(height: 20),
                  Expanded(child: _buildBodyContent(context)),
                ],
              ),
            ),
    );
  }

  // ======== Sub-Widgets ===========
  Widget _buildSearchBar(BuildContext context) {
    return CustomTextField(
      hintText: "Search",
      title: _getSearchBarTitle(widget.name),
      prefixIcon: Icon(
        Symbols.search,
        color: Theme.of(context).colorScheme.onSurface,
      ),
      onChanged: (text) {
        getoperatorsList(text);
      },
    );
  }

  Widget _buildRadioButtonSwitcher(BuildContext context) {
    return Container(
      height: 40,
      width: MediaQuery.of(context).size.width,
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 1,
            child: RadioListTile(
              fillColor: WidgetStateProperty.resolveWith(
                (states) => states.contains(WidgetState.selected)
                    ? FsColor.primarypayment
                    : Colors.grey,
              ),
              value: 0,
              groupValue: _groupValue,
              title: Text(
                "Prepaid",
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
              ),
              onChanged: (dynamic newValue) {
                setState(() {
                  isLoading = true;
                  _groupValue = newValue;
                });
                getoperatorsList("");
              },
              activeColor: FsColor.primarypayment,
              selected: false,
            ),
          ),
          Expanded(
            flex: 1,
            child: RadioListTile(
              fillColor: WidgetStateProperty.resolveWith(
                (states) => states.contains(WidgetState.selected)
                    ? FsColor.primarypayment
                    : Colors.grey,
              ),
              value: 1,
              groupValue: _groupValue,
              title: Text(
                "Postpaid",
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
              ),
              onChanged: (dynamic newValue) {
                setState(() {
                  isLoading = true;
                  _groupValue = newValue;
                });
                getoperatorsList("");
              },
              activeColor: FsColor.primarypayment,
              selected: false,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainTitle(BuildContext context) {
    final String text;
    if (widget.name == FsString.MUNICIPALITY) {
      text = 'All Corporations';
    } else if (widget.name == FsString.DTH_RECHARGE ||
        widget.name == FsString.PREPAID_MOBILE_RECHARGE ||
        widget.name == FsString.BROADBAND_RECHARGE ||
        widget.name == FsString.CABLE ||
        widget.name == FsString.DATACARD_RECHARGE ||
        widget.name == 'datacard prepaid' ||
        widget.name == 'datacard postpaid' ||
        widget.name == FsString.LANDLINE_BILL ||
        widget.name == FsString.POSTPAID_MOBILE_RECHARGE) {
      text = "All Operators";
    } else if (widget.name == FsString.DIGITAL_VOUCHAR) {
      text = "Operator";
    } else if (widget.name == FsString.FASTAG_RECHARGE) {
      text = "All Banks";
    } else if (widget.name == FsString.GAS_BILL) {
      text = "All Providers";
    } else if (widget.name == FsString.INSURANCE) {
      text = "All Insurance";
    } else {
      text = "All Boards";
    }

    return Text(
      text,
      style: Theme.of(context).textTheme.headlineLarge,
    );
  }

  Widget _buildBodyContent(BuildContext context) {
    // No Operator Found
    if (!isLoading && billersList!.isEmpty) {
      return _noOperatorsFoundAnimation(context);
    }
    // Loading Shimmer
    if (isLoading) {
      return _getShimmeringListLoaderWidget();
    }
    // No Results Found
    if (billersList!.isEmpty) {
      return _noResultFound();
    }
    // Operators List
    return Expanded(
      child: ListView.builder(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).size.height * 0.5,
        ),
        primary: false,
        shrinkWrap: true,
        itemCount: billersList?.length ?? 0,
        itemBuilder: (BuildContext context, int index) {
          final Map place = billersList![index];
          log("billersList: ${billersList.toString()} ");
          log("billersList.length: ${billersList!.length} ");

          return Column(
            children: [
              InkWell(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) =>

                          //  TempOnepayPostpaid(),

                          AddBillerConsumerNo(
                        from_dashboard: widget.from_dashboard,
                        recentRecharge: widget.recentRecharge,
                        selectedOption: widget.name,
                        payBill: payElectricityBill,
                        operatorsData: place,
                        payBillApi: billPaymentApi,
                        billersDetail: billersDetail,
                      ),
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 5.0),
                  child: Row(
                    children: <Widget>[
                      // Operator Image Widget
                      if (
                          // widget.name != FsString.FASTAG_RECHARGE &&
                          widget.name != FsString.DIGITAL_VOUCHAR)
                        _buildOperatorIcon(place),
                      Expanded(
                        child: Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            '${place["Operator Name"]}',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                      ),
                      // Arrow Icon Widget
                      Icon(
                        Symbols.north_east,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ],
                  ),
                ),
              ),
              Divider(thickness: 0.2),
            ],
          );
        },
      ),
    );
  }

  Widget _buildOperatorIcon(Map place) {
    log("operator_id:::::::::: ${place["Operator Id"]} ");
    log("operator_id::::::::::");
    return Container(
      margin: EdgeInsets.only(right: 10),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(5),
        child: Image.network(
            height: 40,
            width: 40,
            fit: BoxFit.cover,
            'https://static.mobikwik.com/appdata/operator_icons/op${place["Operator Id"]}.png'),

        // CachedNetworkImage(
        //   height: 40,
        //   width: 40,
        //   fit: BoxFit.cover,
        //   imageUrl: place["Operator Id"] == 144
        //       ? 'https://static.mobikwik.com/appdata/operator_icons/op143.png'
        //       : 'https://static.mobikwik.com/appdata/operator_icons/op${place["Operator Id"]}.png',
        //   placeholder: (context, url) => Image.asset(
        //     'assets/img/loading.gif',
        //     fit: BoxFit.cover,
        //     width: 40,
        //     height: 40,
        //   ),
        //   errorWidget: (context, url, error) => Icon(Icons.error),
        // ),
      ),
    );
  }

  Widget _noOperatorsFoundAnimation(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: Column(
        children: [
          Lottie.network(
            'https://fstech-cms-db.s3.ap-south-1.amazonaws.com/animation_no_history_86c6a1b597.json',
            height: MediaQuery.of(context).size.height * 0.4,
          ),
          Text(
            'No operator found!',
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: 18,
                  letterSpacing: 1.0,
                  height: 1.5,
                ),
          ),
        ],
      ),
    );
  }

  // ======== Error/Empty/Loader Widgets ===========
  Widget _buildErrorWidget() {
    return Center(
      child: WidgetUtils.getErrorWidget(
        module: AppConstant.ONE_PAY,
        errorMsg: _loadErrorMsg!,
        errorType: _loadErrorType,
        showErrorIcon: true,
        shouldRetry: LoadingErrorUtils.canRetry(_loadErrorType),
        onRetryPressed: () {
          _loadOperatorList();
        },
        retryButtonColor: FsColor.primarypayment,
      ),
    );
  }

  Widget _noResultFound() {
    return Container(
      color: Colors.white,
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            "images/interest/zoom_search_outline.gif",
            height: 180,
            width: 180,
            fit: BoxFit.fill,
          ),
          SizedBox(height: 5),
          Padding(
            padding: EdgeInsets.only(left: 10, right: 10),
            child: Text(
              "Sorry, no results found!",
              style: TextStyle(
                color: FsColorShop.second_color,
                fontSize: FSTextStyle.h6size,
                fontWeight: FontWeight.w800,
                fontFamily: 'Gilroy-SemiBold',
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10, right: 10),
            child: Text(
              "Please check the spelling or try searching for something else",
              maxLines: 10,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: FsColorShop.second_color,
                fontSize: FSTextStyle.h6size,
                fontFamily: 'Gilroy-SemiBold',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _getShimmeringListLoaderWidget() {
    return GroceryUiUtils.operatorsListLoader();
  }

  // ======== Helper Methods ===========
  String _getSearchBarTitle(String? name) {
    if (name == FsString.DTH_RECHARGE ||
        name == FsString.PREPAID_MOBILE_RECHARGE ||
        name == FsString.BROADBAND_RECHARGE ||
        name == FsString.CABLE ||
        name == FsString.DATACARD_RECHARGE ||
        name == 'datacard prepaid' ||
        name == FsString.LANDLINE_BILL ||
        name == FsString.POSTPAID_MOBILE_RECHARGE) {
      return " Operator Name";
    } else if (name == FsString.FASTAG_RECHARGE) {
      return ' FASTag issuer bank';
    } else if (name == FsString.GAS_BILL) {
      return ' Gas Provider Name';
    } else if (name == FsString.INSURANCE) {
      return ' Insurance Name';
    } else {
      return " Board";
    }
  }

  String _getPageTitle(String? name) {
    if (name == FsString.MUNICIPALITY) {
      return 'Select Corporation';
    } else if (name == FsString.DTH_RECHARGE ||
        name == FsString.PREPAID_MOBILE_RECHARGE ||
        name == FsString.BROADBAND_RECHARGE ||
        name == FsString.CABLE ||
        name == FsString.DATACARD_RECHARGE ||
        name == 'datacard prepaid' ||
        name == FsString.LANDLINE_BILL ||
        name == FsString.POSTPAID_MOBILE_RECHARGE ||
        name == FsString.DIGITAL_VOUCHAR) {
      return 'Operator';
    } else if (name == FsString.FASTAG_RECHARGE) {
      return 'FASTag Issuer Bank';
    } else if (name == FsString.GAS_BILL) {
      return 'Gas Provider';
    } else if (name == FsString.INSURANCE) {
      return 'Insurance';
    } else {
      return 'Board';
    }
  }

  void radioBtnVisibility() {
    if (_groupValue == 0) {
      widget.name = "datacard prepaid";
    } else if (_groupValue == 1) {
      widget.name = "datacard postpaid";
    }
  }

  // ======== Core Methods ===========
  void _loadOperatorList() {
    AppUtils.checkInternetConnection().then((value) {
      if (value) {
        _loadError = false;
        _loadErrorType = null;
        _loadErrorMsg = null;
        getOperators();
      } else {
        _loadError = true;
        _loadErrorMsg = FsString.ERROR_NO_INTERNET_RETRY;
        _loadErrorType = LoadingErrorType.INTERNET;
      }
      setState(() {});
    });
  }

  void getOperators() {
    mobikwikPresenter = MobikwikPresenter(this);
    if (widget.name == "DataCard") {
      _groupValue = 0;
      widget.name = 'datacard prepaid';
    }
    getoperatorsList("");
  }

  // This method will get the operator list from the presenter
  void getoperatorsList(String searchText) {
    if (widget.name == FsString.PREPAID_MOBILE_RECHARGE) {
      mobikwikPresenter.getOperatorData(
        "getBillersList",
        searchText,
        "prepaid",
      );
    } else if (widget.name == FsString.EMI_PAYMENT) {
      mobikwikPresenter.getOperatorData(
        "getBillersList",
        searchText,
        "emi",
      );
    } else {
      radioBtnVisibility();
      mobikwikPresenter.getOperatorData(
        "getBillersList",
        searchText,
        widget.name,
      );
    }
    setState(() {});
  }

  // ======== Payment / Bill Methods ===========
  void payElectricityBill() {
    mobikwikPresenter.payBill(
      callingType: "payElectricityBill",
      consumerNo: "'676272812",
      operatorId: "31",
      billAmount: "400",
    );
  }

  // Called from AddBillerConsumerNo
  void billPaymentApi({
    order_id,
    op_name,
    amt,
    cn,
    op,
    pId,
    billerDetail,
    operatorDetail,
  }) {
    setState(() {
      billersDetail = billerDetail;
      op_Detail = operatorDetail;
    });
    mobikwikPresenter.billPayment(
      callingType: "billPayment",
      type: widget.name,
      order_id: order_id,
      op_name: op_name,
      amount: amt,
      operatorid: op,
      cn: cn,
      paymentId: pId,
    );
  }

  void viewBillpage(Map data) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ViewBillerDetails(
          operatorDetail: op_Detail,
          payBillApi: billPaymentApi,
          billersDetail: data,
        ),
      ),
    );
  }

  void paymentStatus(dynamic paymentStatus, dynamic paymentInfo) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BillPaymentSuccussful(
          selectedOtion: widget.name,
          customerDetail: billersDetail,
          operatorsDetail: op_Detail,
          paymentStatus: paymentStatus,
          paymentData: paymentInfo,
        ),
      ),
    );
  }

  // ======== MobikwikView Interface ===========
  @override
  ordersuccess(success, {callingType, String? searchedText}) {}

  @override
  orderfailure(failed, {callingType}) {}

  @override
  ordererror(error, {callingType}) {}

  @override
  success(success, {callingType, String? searchedText}) {
    if (callingType == "getBillersList") {
      if (success['data'].isNotEmpty || success != null) {
        isLoading = false;
        billersList = success['data'];
      }
      setState(() {});
    } else if (callingType == "billPayment") {
      if (success != null) {
        print("billpayment$success");
        paymentStatus(success['data']['status'], success['data']);
      }
    } else if (callingType == "payElectricityBill") {
      if (success != null) {
        print("bill success$success");
      }
    }
    setState(() {});
  }

  @override
  failure(failed, {callingType}) {
    // if (callingType == "viewConsumerBill") {
    //   print("failure:" + failed.toString());
    // } else
    if (callingType == "billPayment") {
      print("failure:$failed");
      paymentStatus("failure", failed['data']);
    }
  }

  @override
  error(error, {callingType}) {
    // if (callingType == "viewConsumerBill") {
    //   Toastly.error(context, 'Invalid customer account.');
    //   print("error:" + error.toString());
    // } else
    if (callingType == "billPayment") {
      print("error:$error");
      paymentStatus("failure", {});
    }
  }
}
