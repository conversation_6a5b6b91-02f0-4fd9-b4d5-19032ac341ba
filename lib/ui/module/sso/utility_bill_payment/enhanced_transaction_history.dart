import 'package:flutter/material.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';

class EnhancedTransactionHistoryScreen extends StatefulWidget {
  @override
  _EnhancedTransactionHistoryScreenState createState() =>
      _EnhancedTransactionHistoryScreenState();
}

class _EnhancedTransactionHistoryScreenState
    extends State<EnhancedTransactionHistoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mobileController = TextEditingController();
  final _dateController = TextEditingController();
  final _transactionRefController = TextEditingController();

  String _searchType = 'mobile_date'; // 'mobile_date' or 'transaction_ref'
  List<Map<String, dynamic>> _transactions = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _mobileController.dispose();
    _dateController.dispose();
    _transactionRefController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MyScrollView(
      pageTitle: 'Transaction History',
      appBarColor: Theme.of(context).primaryColor,
      actions: [
        SecuredByBBPS(),
      ],
      pageBody: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search Form
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Search Transactions By:',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      SizedBox(height: 12),

                      // Search Type Selection
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<String>(
                              title: Text('Mobile & Date'),
                              value: 'mobile_date',
                              groupValue: _searchType,
                              onChanged: (value) {
                                setState(() {
                                  _searchType = value!;
                                  _transactions.clear();
                                });
                              },
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<String>(
                              title: Text('Transaction ID'),
                              value: 'transaction_ref',
                              groupValue: _searchType,
                              onChanged: (value) {
                                setState(() {
                                  _searchType = value!;
                                  _transactions.clear();
                                });
                              },
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 16),

                      // Search Fields
                      if (_searchType == 'mobile_date') ...[
                        // Mobile Number Field
                        TextFormField(
                          controller: _mobileController,
                          decoration: InputDecoration(
                            labelText: 'Mobile Number',
                            hintText: 'Enter mobile number',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.phone),
                          ),
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter mobile number';
                            }
                            if (value.length != 10) {
                              return 'Please enter valid 10-digit mobile number';
                            }
                            return null;
                          },
                        ),

                        SizedBox(height: 16),

                        // Date Field
                        TextFormField(
                          controller: _dateController,
                          decoration: InputDecoration(
                            labelText: 'Transaction Date',
                            hintText: 'Select date',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.calendar_today),
                            suffixIcon: IconButton(
                              icon: Icon(Icons.calendar_month),
                              onPressed: () => _selectDate(context),
                            ),
                          ),
                          readOnly: true,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select transaction date';
                            }
                            return null;
                          },
                        ),
                      ] else ...[
                        // Transaction Reference ID Field
                        TextFormField(
                          controller: _transactionRefController,
                          decoration: InputDecoration(
                            labelText: 'Transaction Reference ID',
                            hintText: 'Enter transaction reference ID',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.receipt_long),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter transaction reference ID';
                            }
                            return null;
                          },
                        ),
                      ],

                      SizedBox(height: 20),

                      // Search Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _searchTransactions,
                          child: Text('Search Transactions'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            SizedBox(height: 20),

            // Results Section
            if (_isLoading)
              Center(child: CircularProgressIndicator())
            else if (_transactions.isNotEmpty) ...[
              Text(
                'Transaction Results (${_transactions.length})',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              SizedBox(height: 12),

              // Transaction List
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: _transactions.length,
                itemBuilder: (context, index) {
                  final transaction = _transactions[index];
                  return _buildTransactionCard(transaction);
                },
              ),
            ] else if (_transactions.isEmpty && !_isLoading)
              Center(
                child: Column(
                  children: [
                    Icon(Icons.search, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'Search for transactions using the form above',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionCard(Map<String, dynamic> transaction) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  transaction['service'] ?? 'Service',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(transaction['status']),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    transaction['status'] ?? 'Unknown',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text('Amount: ₹${transaction['amount'] ?? '0'}'),
            Text('Date: ${transaction['date'] ?? 'N/A'}'),
            Text('Transaction ID: ${transaction['transactionId'] ?? 'N/A'}'),
            if (transaction['mobile'] != null)
              Text('Mobile: ${transaction['mobile']}'),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'success':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _dateController.text = "${picked.day}/${picked.month}/${picked.year}";
      });
    }
  }

  void _searchTransactions() {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      Future.delayed(Duration(seconds: 2), () {
        setState(() {
          _isLoading = false;
          // Mock transaction data
          _transactions = [
            {
              'service': 'Mobile Prepaid',
              'amount': '299',
              'date': '15/12/2024',
              'transactionId': 'TXN123456789',
              'mobile': _searchType == 'mobile_date'
                  ? _mobileController.text
                  : '9876543210',
              'status': 'Success',
            },
            {
              'service': 'Electricity Bill',
              'amount': '1250',
              'date': '14/12/2024',
              'transactionId': 'TXN987654321',
              'mobile': _searchType == 'mobile_date'
                  ? _mobileController.text
                  : '9876543210',
              'status': 'Success',
            },
          ];
        });
      });
    }
  }
}
