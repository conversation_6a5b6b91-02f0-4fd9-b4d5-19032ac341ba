import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:sso_futurescape/custom_widgets/custom_form_widget.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_presenter.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_view.dart';
import 'package:sso_futurescape/ui/base/loading_constants.dart';
import 'package:sso_futurescape/ui/module/meeting/utils/grocery_ui_utils.dart';
import 'package:sso_futurescape/utils/app_constant.dart';
import 'package:sso_futurescape/utils/app_utils.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';
import 'package:sso_futurescape/utils/ui/loading_error_utils.dart';
import 'package:common_config/utils/toast/toast.dart';

class EnhancedTransactionHistory extends StatefulWidget {
  @override
  _EnhancedTransactionHistoryState createState() => _EnhancedTransactionHistoryState();
}

class _EnhancedTransactionHistoryState extends State<EnhancedTransactionHistory> 
    implements MobikwikView {
  
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _transactionIdController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  
  late MobikwikPresenter mobikwikPresenter;
  
  bool isLoading = false;
  bool isLoadingHistory = false;
  bool loadingsearch = false;
  bool _loadError = false;
  LoadingErrorType? _loadErrorType;
  String? _loadErrorMsg;
  
  List? paymentHistory = [];
  Map? txnHistory;
  
  // Search options
  bool _isSearchByMobile = true;
  DateTime? _selectedDate;
  
  @override
  void initState() {
    super.initState();
    mobikwikPresenter = MobikwikPresenter(this);
    _loadPaymentHistory();
  }

  @override
  void dispose() {
    _mobileController.dispose();
    _transactionIdController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadPaymentHistory() {
    AppUtils.checkInternetConnection().then((value) {
      if (value) {
        _loadError = false;
        _loadErrorType = null;
        _loadErrorMsg = null;
        getPaymentHistory("");
      } else {
        _loadError = true;
        _loadErrorType = LoadingErrorType.NO_INTERNET;
        _loadErrorMsg = "No internet connection";
      }
      setState(() {});
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _performSearch() {
    if (_isSearchByMobile) {
      if (_mobileController.text.isEmpty) {
        Toastly.error(context, "Please enter mobile number");
        return;
      }
      if (_selectedDate == null) {
        Toastly.error(context, "Please select a date");
        return;
      }
      _searchByMobileAndDate();
    } else {
      if (_transactionIdController.text.isEmpty) {
        Toastly.error(context, "Please enter transaction reference ID");
        return;
      }
      _searchByTransactionId();
    }
  }

  void _searchByMobileAndDate() {
    String mobile = _mobileController.text;
    String date = DateFormat('yyyy-MM-dd').format(_selectedDate!);
    String searchQuery = "$mobile|$date";
    
    setState(() {
      loadingsearch = true;
    });
    
    getPaymentHistory(searchQuery);
  }

  void _searchByTransactionId() {
    String transactionId = _transactionIdController.text;
    
    setState(() {
      loadingsearch = true;
    });
    
    getPaymentHistory(transactionId);
  }

  void getPaymentHistory(String searchText) {
    SsoStorage.getUserProfile().then((userProfile) {
      mobikwikPresenter.getPaymentHistory(
          callingType: "getPaymentHistory",
          searchText: searchText,
          userId: userProfile['user_id']);
    });
  }

  Widget _buildSearchSection() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Search Transactions',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16),
          
          // Search option toggle
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _isSearchByMobile = true;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: _isSearchByMobile 
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.surface,
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Mobile + Date',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: _isSearchByMobile 
                            ? Theme.of(context).colorScheme.onPrimary
                            : Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _isSearchByMobile = false;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: !_isSearchByMobile 
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.surface,
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Transaction ID',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: !_isSearchByMobile 
                            ? Theme.of(context).colorScheme.onPrimary
                            : Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          // Search form fields
          if (_isSearchByMobile) ...[
            // Mobile number field
            CustomFormWidget(
              controller: _mobileController,
              labelText: 'Mobile Number',
              hintText: 'Enter mobile number',
              keyboardType: TextInputType.phone,
              prefixIcon: Icon(Symbols.phone),
            ),
            SizedBox(height: 12),
            
            // Date picker field
            GestureDetector(
              onTap: () => _selectDate(context),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Symbols.calendar_today),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _selectedDate == null 
                            ? 'Select Date'
                            : DateFormat('dd/MM/yyyy').format(_selectedDate!),
                        style: TextStyle(
                          color: _selectedDate == null 
                              ? Theme.of(context).colorScheme.onSurface.withOpacity(0.6)
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                    Icon(Symbols.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ] else ...[
            // Transaction reference ID field
            CustomFormWidget(
              controller: _transactionIdController,
              labelText: 'Transaction Reference ID',
              hintText: 'Enter transaction reference ID',
              prefixIcon: Icon(Symbols.receipt_long),
            ),
          ],
          
          SizedBox(height: 16),
          
          // Search button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _performSearch,
              icon: Icon(Symbols.search),
              label: Text('Search'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MyScrollView(
      pageTitle: 'Transaction History',
      actions: [
        SecuredByBBPS(),
      ],
      pageBody: Column(
        children: [
          _buildSearchSection(),
          
          // Results section
          if (isLoading) ...[
            Container(
              height: 200,
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          ] else if (paymentHistory != null && paymentHistory!.isNotEmpty) ...[
            // Transaction list would go here
            Container(
              padding: EdgeInsets.all(16),
              child: Text(
                'Found ${paymentHistory!.length} transactions',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ),
            // TODO: Add transaction list widget here
          ] else if (loadingsearch) ...[
            Container(
              height: 200,
              child: Center(
                child: Text(
                  'No transactions found',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // MobikwikView implementation
  @override
  void success(success, {callingType, String? searchedText}) {
    if (callingType == "getPaymentHistory") {
      if (success != null) {
        paymentHistory = success['data'];
        isLoading = false;
        loadingsearch = true;
      }
      setState(() {});
      log("Payment history success: " + success.toString());
    }
  }

  @override
  void failure(failure, {callingType}) {
    isLoading = false;
    loadingsearch = true;
    setState(() {});
    log("Payment history failure: " + failure.toString());
  }

  @override
  void error(error, {callingType}) {
    isLoading = false;
    loadingsearch = true;
    setState(() {});
    log("Payment history error: " + error.toString());
  }

  @override
  void ordersuccess(success, {callingType, String? searchedText}) {}

  @override
  void orderfailure(failed, {callingType}) {}

  @override
  void ordererror(error, {callingType}) {}
}
