import 'package:flutter/material.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';
import 'package:sso_futurescape/custom_widgets/b_assured_logo_widget.dart';

class EnhancedTransactionHistoryScreen extends StatefulWidget {
  @override
  _EnhancedTransactionHistoryScreenState createState() =>
      _EnhancedTransactionHistoryScreenState();
}

class _EnhancedTransactionHistoryScreenState
    extends State<EnhancedTransactionHistoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mobileController = TextEditingController();
  final _fromDateController = TextEditingController();
  final _toDateController = TextEditingController();
  final _transactionRefController = TextEditingController();

  String _searchType = 'mobile_date'; // 'mobile_date' or 'transaction_ref'
  List<Map<String, dynamic>> _transactions = [];
  bool _isLoading = false;
  bool _hasSearched = false;
  DateTime? _fromDate;
  DateTime? _toDate;

  @override
  void dispose() {
    _mobileController.dispose();
    _fromDateController.dispose();
    _toDateController.dispose();
    _transactionRefController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MyScrollView(
      pageTitle: 'Transaction History',
      appBarColor: Theme.of(context).primaryColor,
      actions: [
        BAssuredLogo(height: 35),
        SecuredByBBPS(),
      ],
      pageBody: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search Form
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Search Transactions By:',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      SizedBox(height: 12),

                      // Search Type Selection
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<String>(
                              title: Text('Mobile & Date'),
                              value: 'mobile_date',
                              groupValue: _searchType,
                              onChanged: (value) {
                                setState(() {
                                  _searchType = value!;
                                  _transactions.clear();
                                  _hasSearched = false;
                                  // Clear form fields when switching search types
                                  _mobileController.clear();
                                  _fromDateController.clear();
                                  _toDateController.clear();
                                  _transactionRefController.clear();
                                  _fromDate = null;
                                  _toDate = null;
                                });
                              },
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<String>(
                              title: Text('Transaction ID'),
                              value: 'transaction_ref',
                              groupValue: _searchType,
                              onChanged: (value) {
                                setState(() {
                                  _searchType = value!;
                                  _transactions.clear();
                                  _hasSearched = false;
                                  // Clear form fields when switching search types
                                  _mobileController.clear();
                                  _fromDateController.clear();
                                  _toDateController.clear();
                                  _transactionRefController.clear();
                                  _fromDate = null;
                                  _toDate = null;
                                });
                              },
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 16),

                      // Search Fields
                      if (_searchType == 'mobile_date') ...[
                        // Mobile Number Field
                        TextFormField(
                          controller: _mobileController,
                          decoration: InputDecoration(
                            labelText: 'Mobile Number *',
                            hintText: 'Enter 10-digit mobile number',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            prefixIcon: Icon(Icons.phone, color: Colors.blue),
                            filled: true,
                            fillColor: Colors.grey.shade50,
                          ),
                          keyboardType: TextInputType.phone,
                          maxLength: 10,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter mobile number';
                            }
                            if (value.length != 10) {
                              return 'Please enter valid 10-digit mobile number';
                            }
                            if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
                              return 'Please enter only numbers';
                            }
                            return null;
                          },
                        ),

                        SizedBox(height: 16),

                        // From Date Field
                        TextFormField(
                          controller: _fromDateController,
                          decoration: InputDecoration(
                            labelText: 'From Date *',
                            hintText: 'Select start date',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            prefixIcon:
                                Icon(Icons.calendar_today, color: Colors.blue),
                            suffixIcon: IconButton(
                              icon: Icon(Icons.calendar_month,
                                  color: Colors.blue),
                              onPressed: () => _selectFromDate(context),
                            ),
                            filled: true,
                            fillColor: Colors.grey.shade50,
                          ),
                          readOnly: true,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select from date';
                            }
                            return null;
                          },
                        ),

                        SizedBox(height: 16),

                        // To Date Field
                        TextFormField(
                          controller: _toDateController,
                          decoration: InputDecoration(
                            labelText: 'To Date *',
                            hintText: 'Select end date',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            prefixIcon:
                                Icon(Icons.calendar_today, color: Colors.blue),
                            suffixIcon: IconButton(
                              icon: Icon(Icons.calendar_month,
                                  color: Colors.blue),
                              onPressed: () => _selectToDate(context),
                            ),
                            filled: true,
                            fillColor: Colors.grey.shade50,
                          ),
                          readOnly: true,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select to date';
                            }
                            if (_fromDate != null && _toDate != null) {
                              if (_toDate!.isBefore(_fromDate!)) {
                                return 'To date must be after from date';
                              }
                              final difference =
                                  _toDate!.difference(_fromDate!).inDays;
                              if (difference > 90) {
                                return 'Date range cannot exceed 90 days';
                              }
                            }
                            return null;
                          },
                        ),
                      ] else ...[
                        // Transaction Reference ID Field
                        TextFormField(
                          controller: _transactionRefController,
                          decoration: InputDecoration(
                            labelText: 'Transaction Reference ID *',
                            hintText: 'Enter transaction reference ID',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            prefixIcon:
                                Icon(Icons.receipt_long, color: Colors.blue),
                            filled: true,
                            fillColor: Colors.grey.shade50,
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter transaction reference ID';
                            }
                            if (value.length < 6) {
                              return 'Transaction ID must be at least 6 characters';
                            }
                            return null;
                          },
                        ),
                      ],

                      SizedBox(height: 20),

                      // Search Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _searchTransactions,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 2,
                          ),
                          child: _isLoading
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    ),
                                    SizedBox(width: 12),
                                    Text('Searching...'),
                                  ],
                                )
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.search, size: 20),
                                    SizedBox(width: 8),
                                    Text(
                                      'Search Transactions',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            SizedBox(height: 20),

            // Results Section
            if (_isLoading)
              Center(child: CircularProgressIndicator())
            else if (_transactions.isNotEmpty) ...[
              Text(
                'Transaction Results (${_transactions.length})',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              SizedBox(height: 12),

              // Transaction List
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: _transactions.length,
                itemBuilder: (context, index) {
                  final transaction = _transactions[index];
                  return _buildTransactionCard(transaction);
                },
              ),
            ] else if (_transactions.isEmpty && !_isLoading && _hasSearched)
              Center(
                child: Column(
                  children: [
                    Icon(Icons.search_off, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'No transactions found',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Try adjusting your search criteria',
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                  ],
                ),
              )
            else if (!_hasSearched && !_isLoading)
              Center(
                child: Column(
                  children: [
                    Icon(Icons.search, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'Search for transactions using the form above',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionCard(Map<String, dynamic> transaction) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      transaction['service'] ?? 'Service',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '₹${transaction['amount'] ?? '0'}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade700,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(transaction['status']),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    transaction['status']?.toUpperCase() ?? 'UNKNOWN',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            Divider(color: Colors.grey.shade300),
            SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.calendar_today,
                    size: 16, color: Colors.grey.shade600),
                SizedBox(width: 8),
                Text(
                  'Date: ${transaction['date'] ?? 'N/A'}',
                  style: TextStyle(color: Colors.grey.shade700),
                ),
              ],
            ),
            SizedBox(height: 6),
            Row(
              children: [
                Icon(Icons.receipt_long, size: 16, color: Colors.grey.shade600),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'ID: ${transaction['transactionId'] ?? 'N/A'}',
                    style: TextStyle(color: Colors.grey.shade700),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            if (transaction['mobile'] != null) ...[
              SizedBox(height: 6),
              Row(
                children: [
                  Icon(Icons.phone, size: 16, color: Colors.grey.shade600),
                  SizedBox(width: 8),
                  Text(
                    'Mobile: ${transaction['mobile']}',
                    style: TextStyle(color: Colors.grey.shade700),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'success':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectFromDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _fromDate ?? DateTime.now().subtract(Duration(days: 30)),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _fromDate = picked;
        _fromDateController.text =
            "${picked.day}/${picked.month}/${picked.year}";
        // Clear to date if it's before the new from date
        if (_toDate != null && _toDate!.isBefore(picked)) {
          _toDate = null;
          _toDateController.clear();
        }
      });
    }
  }

  Future<void> _selectToDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _toDate ?? (_fromDate?.add(Duration(days: 1)) ?? DateTime.now()),
      firstDate: _fromDate ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _toDate = picked;
        _toDateController.text = "${picked.day}/${picked.month}/${picked.year}";
      });
    }
  }

  void _searchTransactions() {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _hasSearched = false;
      });

      // Simulate API call with enhanced search parameters
      Future.delayed(Duration(seconds: 2), () {
        setState(() {
          _isLoading = false;
          _hasSearched = true;

          // Enhanced mock transaction data based on search type
          if (_searchType == 'mobile_date') {
            _transactions = _generateMockTransactionsByMobileAndDate();
          } else {
            _transactions = _generateMockTransactionsByRefId();
          }
        });
      });
    }
  }

  List<Map<String, dynamic>> _generateMockTransactionsByMobileAndDate() {
    // Simulate different scenarios based on mobile number
    final mobile = _mobileController.text;

    if (mobile.startsWith('9876')) {
      return [
        {
          'service': 'Mobile Prepaid',
          'amount': '299',
          'date': _fromDateController.text,
          'transactionId': 'TXN${DateTime.now().millisecondsSinceEpoch}',
          'mobile': mobile,
          'status': 'Success',
        },
        {
          'service': 'Electricity Bill',
          'amount': '1250',
          'date': _toDateController.text,
          'transactionId': 'TXN${DateTime.now().millisecondsSinceEpoch + 1}',
          'mobile': mobile,
          'status': 'Success',
        },
        {
          'service': 'DTH Recharge',
          'amount': '450',
          'date': _fromDateController.text,
          'transactionId': 'TXN${DateTime.now().millisecondsSinceEpoch + 2}',
          'mobile': mobile,
          'status': 'Pending',
        },
      ];
    } else if (mobile.startsWith('8765')) {
      return [
        {
          'service': 'Gas Bill',
          'amount': '890',
          'date': _fromDateController.text,
          'transactionId': 'TXN${DateTime.now().millisecondsSinceEpoch + 3}',
          'mobile': mobile,
          'status': 'Failed',
        },
      ];
    } else {
      return []; // No results for other numbers
    }
  }

  List<Map<String, dynamic>> _generateMockTransactionsByRefId() {
    final refId = _transactionRefController.text.toUpperCase();

    if (refId.contains('TXN123') || refId.contains('SUCCESS')) {
      return [
        {
          'service': 'Mobile Postpaid',
          'amount': '599',
          'date': '20/12/2024',
          'transactionId': refId,
          'mobile': '9876543210',
          'status': 'Success',
        },
      ];
    } else if (refId.contains('FAIL') || refId.contains('ERROR')) {
      return [
        {
          'service': 'Water Bill',
          'amount': '340',
          'date': '19/12/2024',
          'transactionId': refId,
          'mobile': '8765432109',
          'status': 'Failed',
        },
      ];
    } else {
      return []; // No results for other reference IDs
    }
  }
}
