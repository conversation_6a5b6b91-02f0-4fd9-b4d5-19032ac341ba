import 'package:flutter/material.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';
import 'package:sso_futurescape/theme/theme.dart';

class ComplaintRegistrationScreen extends StatefulWidget {
  @override
  _ComplaintRegistrationScreenState createState() =>
      _ComplaintRegistrationScreenState();
}

class _ComplaintRegistrationScreenState
    extends State<ComplaintRegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mobileController = TextEditingController();
  final _dateController = TextEditingController();
  final _transactionRefController = TextEditingController();
  final _complaintController = TextEditingController();

  String _searchType = 'mobile_date'; // 'mobile_date' or 'transaction_ref'

  @override
  void dispose() {
    _mobileController.dispose();
    _dateController.dispose();
    _transactionRefController.dispose();
    _complaintController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MyScrollView(
      pageTitle: 'Complaint Registration',
      appBarColor: Theme.of(context).primaryColor,
      actions: [
        SecuredByBBPS(),
      ],
      pageBody: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search Type Selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Search Transaction By:',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<String>(
                              title: Text('Mobile & Date'),
                              value: 'mobile_date',
                              groupValue: _searchType,
                              onChanged: (value) {
                                setState(() {
                                  _searchType = value!;
                                });
                              },
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<String>(
                              title: Text('Transaction ID'),
                              value: 'transaction_ref',
                              groupValue: _searchType,
                              onChanged: (value) {
                                setState(() {
                                  _searchType = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 20),

              // Search Fields
              if (_searchType == 'mobile_date') ...[
                // Mobile Number Field
                TextFormField(
                  controller: _mobileController,
                  decoration: InputDecoration(
                    labelText: 'Mobile Number',
                    hintText: 'Enter mobile number',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.phone),
                  ),
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter mobile number';
                    }
                    if (value.length != 10) {
                      return 'Please enter valid 10-digit mobile number';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 16),

                // Date Field
                TextFormField(
                  controller: _dateController,
                  decoration: InputDecoration(
                    labelText: 'Transaction Date',
                    hintText: 'Select date',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                    suffixIcon: IconButton(
                      icon: Icon(Icons.calendar_month),
                      onPressed: () => _selectDate(context),
                    ),
                  ),
                  readOnly: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select transaction date';
                    }
                    return null;
                  },
                ),
              ] else ...[
                // Transaction Reference ID Field
                TextFormField(
                  controller: _transactionRefController,
                  decoration: InputDecoration(
                    labelText: 'Transaction Reference ID',
                    hintText: 'Enter transaction reference ID',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.receipt_long),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter transaction reference ID';
                    }
                    return null;
                  },
                ),
              ],

              SizedBox(height: 20),

              // Complaint Description
              TextFormField(
                controller: _complaintController,
                decoration: InputDecoration(
                  labelText: 'Complaint Description',
                  hintText: 'Describe your complaint in detail',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 4,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter complaint description';
                  }
                  return null;
                },
              ),

              SizedBox(height: 30),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _submitComplaint,
                  child: Text('Submit Complaint'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _dateController.text = "${picked.day}/${picked.month}/${picked.year}";
      });
    }
  }

  void _submitComplaint() {
    if (_formKey.currentState!.validate()) {
      // TODO: Implement complaint submission logic
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Complaint submitted successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      // Clear form
      _mobileController.clear();
      _dateController.clear();
      _transactionRefController.clear();
      _complaintController.clear();
      setState(() {
        _searchType = 'mobile_date';
      });
    }
  }
}
