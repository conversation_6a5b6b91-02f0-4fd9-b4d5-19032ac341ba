import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:sso_futurescape/custom_widgets/custom_elevated_btn.dart';
import 'package:sso_futurescape/custom_widgets/custom_form_widget.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';
import 'package:common_config/utils/toast/toast.dart';

class ComplaintRegistrationScreen extends StatefulWidget {
  @override
  _ComplaintRegistrationScreenState createState() =>
      _ComplaintRegistrationScreenState();
}

class _ComplaintRegistrationScreenState
    extends State<ComplaintRegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mobileController = TextEditingController();
  final _transactionIdController = TextEditingController();
  DateTime? _selectedDate;

  // Toggle between search options
  bool _isSearchByMobile = true;

  @override
  void dispose() {
    _mobileController.dispose();
    _transactionIdController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _submitComplaint() {
    if (_formKey.currentState!.validate()) {
      if (_isSearchByMobile) {
        if (_selectedDate == null) {
          Toastly.error(context, "Please select a date");
          return;
        }
        // Process mobile + date search
        _searchByMobileAndDate();
      } else {
        // Process transaction reference ID search
        _searchByTransactionId();
      }
    }
  }

  void _searchByMobileAndDate() {
    // TODO: Implement API call for mobile + date search
    String mobile = _mobileController.text;
    String date = _selectedDate!.toIso8601String().split('T')[0];

    Toastly.success(
        context, "Searching complaints for mobile: $mobile on date: $date");
    // Navigate to complaint details or results screen
  }

  void _searchByTransactionId() {
    // TODO: Implement API call for transaction reference ID search
    String transactionId = _transactionIdController.text;

    Toastly.success(
        context, "Searching complaint for transaction ID: $transactionId");
    // Navigate to complaint details or results screen
  }

  @override
  Widget build(BuildContext context) {
    return MyScrollView(
      pageTitle: 'Complaint Registration',
      actions: [
        SecuredByBBPS(),
      ],
      pageBody: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search option toggle
            Container(
              margin: EdgeInsets.symmetric(vertical: 16),
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _isSearchByMobile = true;
                        });
                      },
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                        decoration: BoxDecoration(
                          color: _isSearchByMobile
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.surface,
                          border: Border.all(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Mobile + Date',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: _isSearchByMobile
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _isSearchByMobile = false;
                        });
                      },
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                        decoration: BoxDecoration(
                          color: !_isSearchByMobile
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.surface,
                          border: Border.all(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Transaction ID',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: !_isSearchByMobile
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Search form fields
            if (_isSearchByMobile) ...[
              // Mobile number field
              TextFormField(
                controller: _mobileController,
                keyboardType: TextInputType.phone,
                decoration: InputDecoration(
                  labelText: 'Mobile Number',
                  hintText: 'Enter your mobile number',
                  prefixIcon: Icon(Symbols.phone),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter mobile number';
                  }
                  if (value.length != 10) {
                    return 'Please enter valid 10-digit mobile number';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),

              // Date picker field
              GestureDetector(
                onTap: () => _selectDate(context),
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.3),
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Symbols.calendar_today),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _selectedDate == null
                              ? 'Select Date'
                              : '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}',
                          style: TextStyle(
                            color: _selectedDate == null
                                ? Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withOpacity(0.6)
                                : Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ),
                      Icon(Symbols.arrow_drop_down),
                    ],
                  ),
                ),
              ),
            ] else ...[
              // Transaction reference ID field
              TextFormField(
                controller: _transactionIdController,
                decoration: InputDecoration(
                  labelText: 'Transaction Reference ID',
                  hintText: 'Enter transaction reference ID',
                  prefixIcon: Icon(Symbols.receipt_long),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter transaction reference ID';
                  }
                  return null;
                },
              ),
            ],

            SizedBox(height: 32),

            // Submit button
            CustomLargeBtn(
              text: 'Search Complaint',
              onPressed: _submitComplaint,
            ),

            SizedBox(height: 16),

            // Help text
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'How to register a complaint:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Use Mobile + Date option if you know the mobile number and transaction date\n'
                    '• Use Transaction ID option if you have the transaction reference number\n'
                    '• Ensure all details are accurate for faster resolution',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
