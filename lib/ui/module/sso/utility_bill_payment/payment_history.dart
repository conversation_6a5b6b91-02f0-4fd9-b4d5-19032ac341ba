import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:sso_futurescape/config/strings/strings.dart';
import 'package:sso_futurescape/custom_widgets/custom_form_widget.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';
import 'package:sso_futurescape/custom_widgets/b_assured_logo_widget.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_presenter.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_view.dart';
import 'package:sso_futurescape/ui/base/loading_constants.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/transaction_successful.dart';
import 'package:sso_futurescape/ui/module/meeting/utils/grocery_ui_utils.dart';
import 'package:sso_futurescape/ui/widgets/my_flutter_app_icons.dart';
import 'package:sso_futurescape/utils/app_constant.dart';
import 'package:sso_futurescape/utils/app_utils.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';
import 'package:sso_futurescape/utils/ui/loading_error_utils.dart';
import 'package:sso_futurescape/utils/widget_utils.dart';
import 'package:sso_futurescape/utils/payment/transaction_status_handler.dart';

// import 'unitbuildingsetup.dart';

class PaymentHistory extends StatefulWidget {
  @override
  _PaymentHistoryState createState() => _PaymentHistoryState();
}

class _PaymentHistoryState extends State<PaymentHistory>
    implements MobikwikView {
  final TextEditingController _searchControl = new TextEditingController();
  late MobikwikPresenter mobikwikPresenter;
  final paymentHistoryKey = GlobalKey<TransactionSuccussfulState>();
  bool loadingsearch = true;
  bool _loadError = false;
  LoadingErrorType? _loadErrorType;
  String? _loadErrorMsg;

  // Enhanced filtering variables
  final _formKey = GlobalKey<FormState>();
  final _mobileController = TextEditingController();
  final _fromDateController = TextEditingController();
  final _toDateController = TextEditingController();
  final _transactionRefController = TextEditingController();
  String _searchType = 'simple'; // 'simple', 'mobile_date', 'transaction_ref'
  bool _showAdvancedFilters = false;
  bool _hasSearched = false;
  DateTime? _fromDate;
  DateTime? _toDate;

  List paymenthistory = [
    {
      "img": "images/paid.png",
      "name": "Paid To",
      "subtext": "*********",
      "amount": "₹ 340",
      "date": "18 Feb 2021",
      "fromaccount": "Debited from",
      "debitimg": "images/upi.png",
      "active": true,
    },
    {
      "img": "images/transfer.png",
      "name": "Transfer To",
      "subtext": "XXXXXXXX0041",
      "amount": "₹ 7,260",
      "date": "18 Feb 2021",
      "fromaccount": "Debited from",
      "debitimg": "images/card.png",
      "active": true,
    },
    {
      "img": "images/gas.png",
      "name": "Cylinder Booking",
      "subtext": "*********",
      "amount": "₹ 694",
      "date": "18 Feb 2021",
      "fromaccount": "Debited from",
      "debitimg": "images/wallet.png",
      "active": true,
    },
    {
      "img": "images/transfer.png",
      "name": "Transfer To",
      "subtext": "John Doe",
      "amount": "₹ 420",
      "date": "18 Feb 2021",
      "fromaccount": "Failed",
      "debitimg": "images/failed.png",
      "active": true,
    },
    {
      "img": "images/smartphone.png",
      "name": "Mobile Recharge",
      "subtext": "**********",
      "amount": "₹ 399",
      "date": "18 Feb 2021",
      "fromaccount": "Debited from",
      "debitimg": "images/online-payment.png",
      "active": true,
    },
    {
      "img": "images/return.png",
      "name": "Return From",
      "subtext": "PAYU",
      "amount": "₹ 880",
      "date": "18 Feb 2021",
      "fromaccount": "Debited from",
      "debitimg": "images/upi.png",
      "active": true,
    }
  ];

  @override
  ordersuccess(success, {callingType, String? searchedText}) {}

  @override
  orderfailure(failed, {callingType}) {}

  @override
  ordererror(error, {callingType}) {}

  @override
  void initState() {
    mobikwikPresenter = new MobikwikPresenter(this);
    super.initState();
    // getPaymentHistory("");
    _loadPaymentHistory();
  }

  var search;

  @override
  Widget build(BuildContext context) {
    return MyScrollView(
      pageTitle: 'Recharges and Utilities History',
      actions: [
        BAssuredLogo(height: 35),
        SizedBox(width: 10),
        SecuredByBBPS(),
      ],
      // isScrollable: false,
      pageBody: isLoadingHistory
          ? Container(
              alignment: Alignment.center,
              margin: EdgeInsetsDirectional.symmetric(
                  vertical: MediaQuery.of(context).size.height * 0.3),
              child: CircularProgressIndicator(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            )
          : (_loadError == true)
              ? Container(
                  color: Colors.white,
                  child: _buildErrorWidget(),
                )
              : isLoading == true && paymentHistory!.isEmpty
                  ? Container(
                      height: MediaQuery.of(context).size.height,
                      child: _getShimmeringListLoaderWidget(),
                    )
                  : isLoading == false &&
                          paymentHistory!.isEmpty &&
                          loadingsearch == true
                      ? Container(
                          alignment: Alignment.center,
                          height: MediaQuery.of(context).size.height,
                          width: MediaQuery.of(context).size.width,
                          child: Text(
                            "Sorry, no results found!",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .merge(TextStyle(
                                    fontSize: 18,
                                    letterSpacing: 1.0,
                                    height: 1.5)),
                          ),
                        )
                      : Column(
                          children: [
                            // Enhanced Search and Filter Section
                            Card(
                              margin: EdgeInsets.all(16),
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Form(
                                  key: _formKey,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Search Transactions',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium
                                                ?.copyWith(
                                                  fontWeight: FontWeight.bold,
                                                ),
                                          ),
                                          TextButton.icon(
                                            onPressed: () {
                                              setState(() {
                                                _showAdvancedFilters =
                                                    !_showAdvancedFilters;
                                                if (!_showAdvancedFilters) {
                                                  _searchType = 'simple';
                                                  _clearAdvancedFilters();
                                                }
                                              });
                                            },
                                            icon: Icon(
                                              _showAdvancedFilters
                                                  ? Icons.expand_less
                                                  : Icons.expand_more,
                                              color: Colors.blue,
                                            ),
                                            label: Text(
                                              _showAdvancedFilters
                                                  ? 'Simple'
                                                  : 'Advanced',
                                              style:
                                                  TextStyle(color: Colors.blue),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 12),
                                      if (!_showAdvancedFilters) ...[
                                        // Simple Search
                                        CustomTextField(
                                          onChanged: (text) {
                                            setState(() {
                                              loadingsearch = false;
                                              search = text;
                                            });
                                            getPaymentHistory(text);
                                          },
                                          title: "Search",
                                          hintText: "Search here...",
                                          lines: 1,
                                          textController: _searchControl,
                                        ),
                                      ] else ...[
                                        // Advanced Filters
                                        _buildAdvancedFilters(),
                                      ],
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.all(0),
                              child: paymentHistory!.isEmpty
                                  ? _noResultFound()
                                  : ListView.separated(
                                      primary: false,
                                      physics: NeverScrollableScrollPhysics(),
                                      shrinkWrap: true,
                                      itemCount: paymentHistory == null
                                          ? 0
                                          : paymentHistory!.length,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        Map place = paymentHistory![index];
                                        setImageIcons(place);
                                        setTypeImage(place);
                                        return Padding(
                                          padding: const EdgeInsets.only(
                                            top: 0,
                                            bottom: 0.0,
                                          ),
                                          child: InkWell(
                                            child: Container(
                                              padding: const EdgeInsets.only(
                                                bottom: 5.0,
                                                top: 5.0,
                                              ),
                                              // height: 85,
                                              child: Row(
                                                children: <Widget>[
                                                  Expanded(
                                                    child: Container(
                                                      padding: EdgeInsets.only(
                                                          top: 10,
                                                          left: 15,
                                                          right: 15,
                                                          bottom: 10),
                                                      alignment:
                                                          Alignment.center,
                                                      child: ListView(
                                                        padding:
                                                            EdgeInsets.zero,
                                                        primary: false,
                                                        physics:
                                                            NeverScrollableScrollPhysics(),
                                                        shrinkWrap: true,
                                                        children: <Widget>[
                                                          Row(
                                                            children: [
                                                              // ClipRRect(
                                                              //   borderRadius:
                                                              //       BorderRadius
                                                              //           .circular(
                                                              //               5),
                                                              //   child:
                                                              //       Image.asset(
                                                              //     typeImage,
                                                              //     height: 35,
                                                              //     width: 35,
                                                              //     fit: BoxFit
                                                              //         .cover,
                                                              //   ),
                                                              // ),
                                                              // SizedBox(
                                                              //     width: 15),
                                                              // ))))
                                                              Expanded(
                                                                  flex: 4,
                                                                  child:
                                                                      Container(
                                                                    alignment:
                                                                        Alignment
                                                                            .topLeft,
                                                                    child: Row(
                                                                      children: <Widget>[
                                                                        Expanded(
                                                                            child:
                                                                                Container(
                                                                          alignment:
                                                                              Alignment.centerLeft,
                                                                          child:
                                                                              Column(
                                                                            crossAxisAlignment:
                                                                                CrossAxisAlignment.start,
                                                                            children: <Widget>[
                                                                              Container(
                                                                                alignment: Alignment.centerLeft,
                                                                                child: RichText(
                                                                                    // maxLines: 1,
                                                                                    text: TextSpan(style: Theme.of(context).textTheme.bodyLarge, children: [
                                                                                  TextSpan(
                                                                                    text: place["type"] != null ? '${place["op_name"]}' : "Type",
                                                                                    style: Theme.of(context).textTheme.bodyLarge,
                                                                                  ),
                                                                                ])),
                                                                              ),
                                                                              SizedBox(height: 3),
                                                                              Text(
                                                                                '${place["cn"]}',
                                                                                style: Theme.of(context).textTheme.bodySmall,
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        )),
                                                                      ],
                                                                    ),
                                                                  )),
                                                              Container(
                                                                alignment: Alignment
                                                                    .centerLeft,
                                                                child: Text(
                                                                  '₹ ${place["amt"].toString()}',
                                                                  style: Theme.of(
                                                                          context)
                                                                      .textTheme
                                                                      .bodySmall!
                                                                      .merge(
                                                                        TextStyle(
                                                                          fontWeight:
                                                                              FontWeight.bold,
                                                                        ),
                                                                      ),
                                                                ),

                                                                // RichText(
                                                                //     text: TextSpan(
                                                                //         style: Theme.of(context).textTheme.titleLarge,
                                                                //         // style: Theme.of(context)
                                                                //         //     .textTheme
                                                                //         //     .bodyLarge,
                                                                //         children: [
                                                                //       TextSpan(
                                                                //         text:
                                                                //             '${place["amt"].toString()}',
                                                                //         style: TextStyle(
                                                                //             fontFamily: 'Gilroy-SemiBold',
                                                                //             fontSize: 14,
                                                                //             color: Colors.black),
                                                                //       ),
                                                                //     ])),
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(height: 10),
                                                          Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              children: [
                                                                Container(
                                                                  alignment:
                                                                      Alignment
                                                                          .centerLeft,
                                                                  child: Column(
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Container(
                                                                        alignment:
                                                                            Alignment.centerLeft,
                                                                        child:
                                                                            Text(
                                                                          place["created_date"] != null
                                                                              ? DateFormat("dd MMM yyyy").format(DateTime.fromMillisecondsSinceEpoch((place["created_date"] * 1000)))
                                                                              : "Date",
                                                                          style: Theme.of(context)
                                                                              .textTheme
                                                                              .bodySmall,
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  child: Row(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .end,
                                                                    children: [
                                                                      Container(
                                                                        alignment:
                                                                            Alignment.centerRight,
                                                                        child: RichText(
                                                                            text: TextSpan(style: Theme.of(context).textTheme.bodyLarge, children: [
                                                                          TextSpan(
                                                                              text: title,
                                                                              style: Theme.of(context).textTheme.bodySmall),
                                                                        ])),
                                                                      ),
                                                                      SizedBox(
                                                                          width:
                                                                              5),
                                                                      ClipRRect(
                                                                        borderRadius:
                                                                            BorderRadius.circular(5),
                                                                        child: title ==
                                                                                "Pending"
                                                                            ? Icon(
                                                                                FlutterIcon.clock_1,
                                                                                size: 17,
                                                                                color: Color.fromRGBO(255, 193, 7, 1.0),
                                                                              )
                                                                            : Image.asset(
                                                                                iconImage!,
                                                                                height: 17,
                                                                                width: 17,
                                                                                fit: BoxFit.cover,
                                                                              ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ]),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            onTap: () {
                                              getTransactionDetail(
                                                  place['reqid'].toString(),
                                                  place);
                                            },
                                          ),
                                        );
                                      },
                                      separatorBuilder: (context, index) {
                                        return Divider(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withOpacity(0.2),
                                          indent: 12,
                                          endIndent: 12,
                                        );
                                      },
                                    ),
                            ),
                          ],
                        ),
    );

    // Stack(
    //   children: [
    //     Scaffold(
    //       bottomNavigationBar: SecuredByBBPS(),
    //       backgroundColor: Theme.of(context).colorScheme.surface,
    //       appBar: new AppBar(
    //         elevation: Theme.of(context).appBarTheme.elevation,
    //         titleTextStyle: Theme.of(context).appBarTheme.titleTextStyle,
    //         backgroundColor: Theme.of(context).colorScheme.surface,
    //         title: Text('Recharges and Utilities History'.toLowerCase()),
    //         leading: BackButton1(),
    //       ),
    //       body: (_loadError == true)
    //           ? Container(color: Colors.white, child: _buildErrorWidget())
    //           : isLoading == true && paymentHistory!.isEmpty
    //               ? Container(
    //                   height: MediaQuery.of(context).size.height,
    //                   child: _getShimmeringListLoaderWidget(),
    //                 )
    //               : isLoading == false &&
    //                       paymentHistory!.isEmpty &&
    //                       loadingsearch == true
    //                   ? Container(
    //                       alignment: Alignment.center,
    //                       height: MediaQuery.of(context).size.height,
    //                       width: MediaQuery.of(context).size.width,
    //                       child: Text(
    //                         "Sorry, no results found!".toLowerCase(),
    //                         style: Theme.of(context)
    //                             .textTheme
    //                             .bodyMedium!
    //                             .merge(TextStyle(
    //                                 fontSize: 18,
    //                                 letterSpacing: 1.0,
    //                                 height: 1.5)),
    //                       ))
    //                   : ListView(
    //                       children: <Widget>[
    //                         Padding(
    //                           padding: EdgeInsets.only(
    //                               top: 15.0, left: 15.0, right: 15.0),
    //                           child: Container(
    //                             decoration: BoxDecoration(
    //                               color: Colors.blueGrey[50],
    //                               borderRadius: BorderRadius.all(
    //                                 Radius.circular(5.0),
    //                               ),
    //                             ),
    //                             child: TextField(
    //                               onChanged: (text) {
    //                                 setState(() {
    //                                   loadingsearch = false;
    //                                   search = text;
    //                                 });
    //                                 getPaymentHistory(text);
    //                               },
    //                               style:
    //                                   Theme.of(context).textTheme.titleMedium,
    //                               decoration: InputDecoration(
    //                                   contentPadding: EdgeInsets.all(10.0),
    //                                   border: OutlineInputBorder(
    //                                     borderRadius:
    //                                         BorderRadius.circular(5.0),
    //                                     borderSide: BorderSide(
    //                                       color: Colors.white,
    //                                     ),
    //                                   ),
    //                                   enabledBorder: OutlineInputBorder(
    //                                     borderSide: BorderSide(
    //                                       color: Colors.white,
    //                                     ),
    //                                     borderRadius:
    //                                         BorderRadius.circular(5.0),
    //                                   ),
    //                                   hintText: "Search here...".toLowerCase(),
    //                                   prefixIcon: Icon(
    //                                     FlutterIcon.search_1,
    //                                     color: Colors.blueGrey[300],
    //                                   ),
    //                                   hintStyle: Theme.of(context)
    //                                       .textTheme
    //                                       .bodyMedium),
    //                               maxLines: 1,
    //                               controller: _searchControl,
    //                             ),
    //                           ),
    //                         ),
    //                         SizedBox(height: 10),
    //                         Padding(
    //                           padding: EdgeInsets.all(0),
    //                           // child: isLoading == true && paymentHistory.isEmpty
    //                           //     ? Container(
    //                           //         height: MediaQuery.of(context).size.height,
    //                           //         child: _getShimmeringListLoaderWidget(),
    //                           //       )
    //                           //     :
    //                           child: paymentHistory!.isEmpty
    //                               ? _noResultFound()
    //                               : ListView.builder(
    //                                   primary: false,
    //                                   physics: NeverScrollableScrollPhysics(),
    //                                   shrinkWrap: true,
    //                                   itemCount: paymentHistory == null
    //                                       ? 0
    //                                       : paymentHistory!.length,
    //                                   itemBuilder:
    //                                       (BuildContext context, int index) {
    //                                     Map place = paymentHistory![index];
    //                                     setImageIcons(place);
    //                                     setTypeImage(place);
    //                                     return Padding(
    //                                       padding: const EdgeInsets.only(
    //                                           bottom: 0.0),
    //                                       child: InkWell(
    //                                         child: Container(
    //                                           padding: const EdgeInsets.only(
    //                                             bottom: 5.0,
    //                                             top: 5.0,
    //                                           ),
    //                                           // height: 85,
    //                                           child: Row(
    //                                             children: <Widget>[
    //                                               Expanded(
    //                                                 child: Container(
    //                                                   padding: EdgeInsets.only(
    //                                                       top: 10,
    //                                                       left: 15,
    //                                                       right: 15,
    //                                                       bottom: 10),
    //                                                   alignment:
    //                                                       Alignment.center,
    //                                                   // height: 80,
    //                                                   // width: MediaQuery.of(context).size.width - 100,
    //                                                   child: ListView(
    //                                                     primary: false,
    //                                                     physics:
    //                                                         NeverScrollableScrollPhysics(),
    //                                                     shrinkWrap: true,
    //                                                     children: <Widget>[
    //                                                       Row(
    //                                                         children: [
    //                                                           ClipRRect(
    //                                                             borderRadius:
    //                                                                 BorderRadius
    //                                                                     .circular(
    //                                                                         5),
    //                                                             child:
    //                                                                 Image.asset(
    //                                                               typeImage,
    //                                                               // "images/electricity.png",
    //                                                               height: 35,
    //                                                               width: 35,
    //                                                               fit: BoxFit
    //                                                                   .cover,
    //                                                             ),
    //                                                           ),
    //                                                           SizedBox(
    //                                                               width: 15),
    //                                                           Expanded(
    //                                                               flex: 4,
    //                                                               child:
    //                                                                   Container(
    //                                                                 alignment:
    //                                                                     Alignment
    //                                                                         .topLeft,
    //                                                                 child: Row(
    //                                                                   children: <Widget>[
    //                                                                     Expanded(
    //                                                                         child:
    //                                                                             Container(
    //                                                                       alignment:
    //                                                                           Alignment.centerLeft,
    //                                                                       child:
    //                                                                           Column(
    //                                                                         crossAxisAlignment:
    //                                                                             CrossAxisAlignment.start,
    //                                                                         children: <Widget>[
    //                                                                           Container(
    //                                                                             alignment: Alignment.centerLeft,
    //                                                                             child: RichText(
    //                                                                                 // maxLines: 1,
    //                                                                                 text: TextSpan(style: Theme.of(context).textTheme.bodyLarge, children: [
    //                                                                               TextSpan(
    //                                                                                   text: place["type"] != null ? '${place["op_name"]}'.toLowerCase() : "Type",
    //                                                                                   // text: place["type"] != null ? '${place["op_name"][0].toUpperCase()}' + place["op_name"].substring(1) : "Type",
    //                                                                                   style: Theme.of(context).textTheme.bodyMedium!.merge(TextStyle(fontWeight: FontWeight.bold))),
    //                                                                             ])),
    //                                                                           ),
    //                                                                           SizedBox(height: 3),
    //                                                                           Container(
    //                                                                             alignment: Alignment.centerLeft,
    //                                                                             child: RichText(
    //                                                                                 text: TextSpan(style: Theme.of(context).textTheme.bodyLarge, children: [
    //                                                                               TextSpan(text: '${place["cn"]}', style: Theme.of(context).textTheme.bodySmall),
    //                                                                             ])),
    //                                                                           ),
    //                                                                         ],
    //                                                                       ),
    //                                                                     )),
    //                                                                   ],
    //                                                                 ),
    //                                                               )),
    //                                                           Expanded(
    //                                                             // flex: 1,
    //                                                             child:
    //                                                                 Container(
    //                                                               alignment:
    //                                                                   Alignment
    //                                                                       .centerRight,
    //                                                               child: RichText(
    //                                                                   text: TextSpan(
    //                                                                       style: Theme.of(context).textTheme.titleLarge,
    //                                                                       // style: Theme.of(context)
    //                                                                       //     .textTheme
    //                                                                       //     .bodyLarge,
    //                                                                       children: [
    //                                                                     TextSpan(
    //                                                                       text:
    //                                                                           '${place["amt"].toString()}',
    //                                                                       style: TextStyle(
    //                                                                           fontFamily: 'Gilroy-SemiBold',
    //                                                                           fontSize: 14,
    //                                                                           color: Colors.black),
    //                                                                     ),
    //                                                                   ])),
    //                                                             ),
    //                                                           ),
    //                                                         ],
    //                                                       ),
    //                                                       SizedBox(height: 10),
    //                                                       Row(
    //                                                           mainAxisAlignment:
    //                                                               MainAxisAlignment
    //                                                                   .spaceBetween,
    //                                                           children: [
    //                                                             Container(
    //                                                               alignment:
    //                                                                   Alignment
    //                                                                       .centerLeft,
    //                                                               child: Column(
    //                                                                 crossAxisAlignment:
    //                                                                     CrossAxisAlignment
    //                                                                         .start,
    //                                                                 children: <Widget>[
    //                                                                   Container(
    //                                                                     alignment:
    //                                                                         Alignment.centerLeft,
    //                                                                     child: RichText(
    //                                                                         text: TextSpan(style: Theme.of(context).textTheme.bodyLarge, children: [
    //                                                                       TextSpan(
    //                                                                         text: place["created_date"] != null
    //                                                                             ? DateFormat("dd MMM yyyy").format(DateTime.fromMillisecondsSinceEpoch((place["created_date"] * 1000)))
    //                                                                             : "Date",
    //                                                                         // '${place["created_date"].toString()}',
    //                                                                         style:
    //                                                                             Theme.of(context).textTheme.bodySmall,
    //                                                                       ),
    //                                                                     ])),
    //                                                                   ),
    //                                                                 ],
    //                                                               ),
    //                                                             ),
    //                                                             Expanded(
    //                                                               child: Row(
    //                                                                 mainAxisAlignment:
    //                                                                     MainAxisAlignment
    //                                                                         .end,
    //                                                                 children: [
    //                                                                   Container(
    //                                                                     alignment:
    //                                                                         Alignment.centerRight,
    //                                                                     child: RichText(
    //                                                                         text: TextSpan(style: Theme.of(context).textTheme.bodyLarge, children: [
    //                                                                       TextSpan(
    //                                                                           text: title!.toLowerCase(),
    //                                                                           // '${place["paymentResponse"]['method']}',
    //                                                                           style: Theme.of(context).textTheme.bodySmall),
    //                                                                     ])),
    //                                                                   ),
    //                                                                   SizedBox(
    //                                                                       width:
    //                                                                           5),
    //                                                                   ClipRRect(
    //                                                                     borderRadius:
    //                                                                         BorderRadius.circular(5),
    //                                                                     child: title ==
    //                                                                             "Pending"
    //                                                                         ? Icon(
    //                                                                             FlutterIcon.clock_1,
    //                                                                             size: 17,
    //                                                                             color: Color.fromRGBO(255, 193, 7, 1.0),
    //                                                                           )
    //                                                                         : Image.asset(
    //                                                                             iconImage!,
    //                                                                             height: 17,
    //                                                                             width: 17,
    //                                                                             fit: BoxFit.cover,
    //                                                                           ),
    //                                                                   ),
    //                                                                 ],
    //                                                               ),
    //                                                             ),
    //                                                           ]),
    //                                                     ],
    //                                                   ),
    //                                                 ),
    //                                               ),
    //                                             ],
    //                                           ),
    //                                         ),
    //                                         onTap: () {
    //                                           getTransactionDetail(
    //                                               place['reqid'].toString(),
    //                                               place);
    //                                         },
    //                                       ),
    //                                     );
    //                                   },
    //                                 ),
    //                         ),
    //                       ],
    //                     ),

    //       // ]),
    //     ),
    //     isLoadingHistory
    //         ? Container(
    //             color: Colors.white.withOpacity(0.7),
    //             alignment: Alignment.center,
    //             child: CircularProgressIndicator(
    //                 valueColor:
    //                     AlwaysStoppedAnimation<Color>(Color(0xFF4080BF))),
    //           )
    //         : Container()
    //   ],
    // );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: WidgetUtils.getErrorWidget(
          module: AppConstant.ONE_PAY,
          errorMsg: _loadErrorMsg!,
          errorType: _loadErrorType,
          showErrorIcon: true,
          shouldRetry: LoadingErrorUtils.canRetry(_loadErrorType),
          onRetryPressed: () {
            _loadPaymentHistory();
          },
          retryButtonColor: Color(0xFF4080BF)),
    );
  }

  void _loadPaymentHistory() {
    AppUtils.checkInternetConnection().then((value) {
      if (value) {
        _loadError = false;
        _loadErrorType = null;
        _loadErrorMsg = null;
        getPaymentHistory("");
      } else {
        _loadError = true;
        _loadErrorMsg = FsString.ERROR_NO_INTERNET_RETRY;
        _loadErrorType = LoadingErrorType.INTERNET;
      }
      setState(() {});
    });
  }

  Widget _noResultFound() {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Lottie.network(
              height: MediaQuery.of(context).size.height * 0.40,
              'https://fstech-cms-db.s3.ap-south-1.amazonaws.com/animation_no_history_86c6a1b597.json'),

          Text("Sorry, no results found!",
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Theme.of(context).colorScheme.onSurface)),
          // Text(
          //     "Please check the spelling or try searching for something else",
          //     maxLines: 10,
          //     textAlign: TextAlign.center,
          //     style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          //         color: Theme.of(context).colorScheme.onSurface)),
        ],
      ),
    );
  }

  String? iconImage, title;
  late var typeImage;

  setTypeImage(Map data) {
    if (data['type'] == "Electricity") {
      typeImage = "images/electricity.png";
    } else if (data['type'] == FsString.PREPAID_MOBILE_RECHARGE) {
      typeImage = "images/smartphone.png";
    } else if (data['type'] == FsString.WATER_BILL) {
      typeImage = "images/water.png";
    } else if (data['type'] == FsString.DTH_RECHARGE) {
      typeImage = "images/dth.png";
    } else if (data['type'] == FsString.GAS_BILL) {
      typeImage = "images/gas-pipe.png";
    } else if (data['type'] == FsString.POSTPAID_MOBILE_RECHARGE) {
      typeImage = "images/smartphone.png";
    } else if (data['type'] == FsString.LANDLINE_BILL) {
      typeImage = "images/landline.png";
    } else if (data['type'] == FsString.BROADBAND_RECHARGE) {
      typeImage = "images/broadband.png";
    } else if (data['type'] == FsString.EMI_PAYMENT) {
      typeImage = "images/emi.png";
    } else if (data['type'] == FsString.INSURANCE) {
      typeImage = "images/insurance.png";
    } else if (data['type'] == FsString.CABLE) {
      typeImage = "images/cable.png";
    } else if (data['type'] == FsString.FASTAG_RECHARGE) {
      typeImage = "images/fastag.png";
    } else if (data['type'] == "datacard prepaid" ||
        data['type'] == "datacard postpaid") {
      typeImage = "images/data card.png";
    } else if (data['type'] == FsString.MUNICIPALITY) {
      typeImage = "images/muncipality.png";
    } else if (data['type'] == FsString.LPG_BOOKING) {
      typeImage = "images/gas.png";
    } else if (data['type'] == FsString.DIGITAL_VOUCHAR) {
      typeImage = "images/google-play.png";
    } else {
      typeImage = "images/electricity.png";
    }
  }

  setImageIcons(Map data) async {
    // First detect the actual transaction status using our comprehensive handler
    TransactionStatus transactionStatus =
        TransactionStatusHandler.detectStatus(data);

    // Set status-based title and icon first
    switch (transactionStatus) {
      case TransactionStatus.success:
        title = "Success";
        iconImage = "images/success.png";
        break;
      case TransactionStatus.pending:
        title = "Pending";
        iconImage = "images/clock.png";
        break;
      case TransactionStatus.failed:
        title = "Failed";
        iconImage = "images/failed.png";
        break;
      case TransactionStatus.unknown:
        title = "Unknown";
        iconImage = "images/unknown.png";
        break;
    }

    // For successful transactions, show payment method details
    if (transactionStatus == TransactionStatus.success) {
      // Check payment method and update title/icon accordingly
      String? paymentMethod = _getPaymentMethod(data);

      if (paymentMethod != null) {
        switch (paymentMethod.toLowerCase()) {
          case "upi":
            title = "Debited From";
            iconImage = "images/upi.png";
            break;
          case "netbanking":
            title = "Debited From";
            iconImage = "images/online-payment.png";
            break;
          case "card":
            title = "Debited From";
            iconImage = "images/card.png";
            break;
          case "wallet":
            title = "Debited From";
            iconImage = "images/wallet.png";
            break;
          case "paylater":
            title = "Pay Later";
            iconImage = "images/paylater.jpg";
            break;
          default:
            title = "Success";
            iconImage = "images/success.png";
            break;
        }
      }
    }

    // Fallback for edge cases
    if (title == null || iconImage == null) {
      title = "Unknown";
      iconImage = "assets/img/loading.gif";
    }
  }

  // Helper method to extract payment method from various response formats
  String? _getPaymentMethod(Map data) {
    // Check various possible locations for payment method
    if (data['paymentResponse'] != null &&
        data['paymentResponse']['method'] != null) {
      return data['paymentResponse']['method'].toString();
    }

    if (data['paymentDetails'] != null) {
      if (data['paymentDetails'] is List && data['paymentDetails'].isNotEmpty) {
        var paymentDetail = data['paymentDetails'][0];
        if (paymentDetail is Map && paymentDetail['method'] != null) {
          return paymentDetail['method'].toString();
        }
      } else if (data['paymentDetails'] is Map &&
          data['paymentDetails']['method'] != null) {
        return data['paymentDetails']['method'].toString();
      }
    }

    if (data['payment_method'] != null) {
      return data['payment_method'].toString();
    }

    return null;
  }

  getPaymentHistory(searchText) {
    SsoStorage.getUserProfile().then((userProfile) {
      mobikwikPresenter.getPaymentHistory(
          callingType: "getPaymentHistory",
          searchText: searchText,
          userId: userProfile['user_id']);
    });
  }

  Map? billDetails;

  getTransactionDetail(reqId, billDetail) {
    billDetails = billDetail;
    isLoadingHistory = true;
    mobikwikPresenter.getTransactionDetail(
        callingType: "getTransactionHistory", reqId: reqId);
    setState(() {});
  }

  Widget _getShimmeringListLoaderWidget() {
    return GroceryUiUtils.historyPageLoader();
  }

  bool isLoadingHistory = false;
  bool isLoading = true;
  List? paymentHistory = [];
  Map? txnHistory = {};

  @override
  error(error, {callingType}) {
    if (callingType == "getPaymentHistory") {
      print("error:" + error.toString());
    } else if (callingType == "getTransactionHistory") {
      print("error:" + error.toString());
    }
  }

  @override
  failure(failed, {callingType}) {
    if (callingType == "getPaymentHistory") {
      print("failed:" + failed.toString());
    } else if (callingType == "getTransactionHistory") {
      print("failed:" + failed.toString());
    }
  }

  @override
  success(success, {callingType, String? searchedText}) {
    if (callingType == "getPaymentHistory") {
      if (success != null) {
        paymentHistory = success['data'];
        isLoading = false;
      }
      setState(() {});
      print("successss:" + success.toString());
    } else if (callingType == "getTransactionHistory") {
      if (success != null) {
        txnHistory = success['data'];
        log("txnHistory: " + txnHistory.toString());
        log("txnHistory transactionData: ${txnHistory}");
        log("txnHistory billingDetails: ${billDetails}");

        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (BuildContext context) {
              return TransactionSuccussful(
                transactionData: txnHistory,
                billingDetails: billDetails,
              );
            },
          ),
        );
        isLoadingHistory = false;

        print("transaction history:" + success.toString());
      }
      setState(() {});
    }
  }
}
