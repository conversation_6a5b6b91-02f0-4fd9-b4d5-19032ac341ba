// ignore_for_file: must_be_immutable, unused_field

import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:common_config/utils/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:sso_futurescape/config/environment/environment.dart';
import 'package:sso_futurescape/config/strings/strings.dart';
import 'package:sso_futurescape/custom_widgets/custom_elevated_btn.dart';
import 'package:sso_futurescape/custom_widgets/custom_form_widget.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';
import 'package:sso_futurescape/custom_widgets/bharat_connect_logo_widget.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_presenter.dart';
import 'package:sso_futurescape/presentor/module/mobikwik_payment/mobikwik_view.dart';
import 'package:sso_futurescape/ui/base/loading_constants.dart';
import 'package:sso_futurescape/ui/module/chsone/payment/bill_payment_successful.dart';
import 'package:sso_futurescape/ui/module/meeting/utils/grocery_ui_utils.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/mobile_recharge_plans.dart';
import 'package:sso_futurescape/utils/app_constant.dart';
import 'package:sso_futurescape/utils/app_utils.dart';
import 'package:sso_futurescape/utils/storage/sso_storage.dart';
import 'package:sso_futurescape/utils/ui/loading_error_utils.dart';
import 'package:sso_futurescape/utils/widget_utils.dart';

import '../../../new_sso/data/local_data_src.dart';

class ViewBillerDetails extends StatefulWidget {
  var operatorId;
  Map? operatorDetail;
  Function? payBillApi;
  Map? billersDetail;
  var selectedOption;
  var mobileNumber;
  var action;
  var amount;

  ViewBillerDetails(
      {this.operatorDetail,
      this.payBillApi,
      this.billersDetail,
      this.selectedOption,
      this.mobileNumber,
      this.action,
      this.amount});

  @override
  _ViewBillerDetailsState createState() =>
      _ViewBillerDetailsState(this.operatorDetail);
}

class _ViewBillerDetailsState extends State<ViewBillerDetails>
    implements MobikwikView {
  final TextEditingController _searchControl = new TextEditingController();
  late Razorpay _razorpay;
  late MobikwikPresenter mobikwikPresenter;
  GlobalKey<MobileRechargePlansState> viewBillKey = GlobalKey();

  var email;
  var mobile;
  var name;
  var desc;
  var amount;
  var fastagRechargeAmount;
  Map? operatorDetail;
  var consumerNo;
  double? dues;
  var billNumber;
  bool isLoading = true;
  bool loadingSuccessPage = false;
  bool _loadError = false;
  LoadingErrorType? _loadErrorType;
  String? _loadErrorMsg;
  String keyClockToken = "";

  getDetail() {
    mobikwikPresenter = new MobikwikPresenter(this);

    SsoStorage.getUserProfile().then((profile) {
      setState(() {
        email = profile['email'];
        mobile = profile['mobile'];
        name = this.operatorDetail!['Operator Name'];
        desc = billersData!['cellNumber'];
      });
    });
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
    functionCall();
  }

//  billersData![
//                                                                         'cn'],
  @override
  void initState() {
    log("ViewBillerDetails widget.selectedOption ${widget.selectedOption}");
    _loadBillDetailsPage();
    getKeyClockToken();
    super.initState();
  }

  Future<String?> getKeyClockToken() async {
    keyClockToken = await LocalDataSource.getAccessToken() ?? "";
    return keyClockToken;
  }

  _ViewBillerDetailsState(this.operatorDetail);

  @override
  Widget build(BuildContext context) {
    log("ViewBillerDetails billersData!['customerName'] ${billersData!['customerName']}");
    return
        // widget.action == "recharge amt"
        //     ? Container(
        //         color: Colors.white,
        //         alignment: Alignment.center,
        //         child: CircularProgressIndicator(
        //             valueColor: AlwaysStoppedAnimation<Color>(
        //                 Theme.of(context).colorScheme.onSurface)),
        //       )
        //     :

        //Confirm once

        GestureDetector(
            onTap: () {
              FocusScope.of(context).requestFocus(FocusNode());
            },
            child: Stack(children: [
              MyScrollView(
                actions: [
                  BharatConnectLogo(height: 35),
                  SizedBox(width: 8),
                  SecuredByBBPS(),
                ],
                isScrollable: false,
                pageTitle: widget.selectedOption ==
                            FsString.PREPAID_MOBILE_RECHARGE ||
                        widget.selectedOption == FsString.DTH_RECHARGE ||
                        widget.selectedOption == "datacard prepaid"
                    ? "Select a recharge plan"
                    : widget.selectedOption == FsString.FASTAG_RECHARGE ||
                            widget.selectedOption == FsString.BROADBAND_RECHARGE
                        ? widget.selectedOption + " Recharge"
                        : ("Pay " + widget.selectedOption + " Bill"),
                pageBody: _loadError
                    ? _buildErrorWidget()
                    : isLoading == true
                        ? _getShimmeringListLoaderWidget()
                        : SizedBox(
                            height: MediaQuery.of(context).size.height,
                            width: MediaQuery.of(context).size.width,
                            child:
                                widget.selectedOption ==
                                            FsString.PREPAID_MOBILE_RECHARGE ||
                                        widget.selectedOption ==
                                            FsString.DTH_RECHARGE ||
                                        widget.selectedOption ==
                                            "datacard prepaid"
                                    ? MobileRechargePlans(
                                        key: viewBillKey,
                                        selectedOption: widget.selectedOption,
                                        doRecharge: dataBeforeBillPayment,
                                        viewRechargePlan: viewRechargePlan,
                                        operatorsData: operatorDetail,
                                        mobileNo: widget.mobileNumber,
                                        rechargePlans: rechargePlans,
                                      )
                                    : billersData!.containsKey('statusMessage')
                                        ? Container()
                                        : Column(
                                            children: <Widget>[
                                              Container(
                                                padding: EdgeInsets.fromLTRB(
                                                    0.0, 5.0, 0.0, 20.0),
                                                child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: <Widget>[
                                                      Container(
                                                        alignment:
                                                            Alignment.topLeft,
                                                        child: Row(
                                                          children: <Widget>[
                                                            widget.selectedOption ==
                                                                    FsString
                                                                        .FASTAG_RECHARGE
                                                                ? Container()
                                                                : ClipRRect(
                                                                    borderRadius:
                                                                        BorderRadius
                                                                            .circular(5),
                                                                    child:
                                                                        CachedNetworkImage(
                                                                      height:
                                                                          48,
                                                                      width: 48,
                                                                      fit: BoxFit
                                                                          .cover,
                                                                      imageUrl: 'https://static.mobikwik.com/appdata/operator_icons/op' +
                                                                          '${widget.operatorDetail!['Operator Id'].toString()}' +
                                                                          '.png',
                                                                      placeholder: (context,
                                                                              url) =>
                                                                          Image
                                                                              .asset(
                                                                        'assets/img/loading.gif',
                                                                        fit: BoxFit
                                                                            .cover,
                                                                        width:
                                                                            48,
                                                                        height:
                                                                            48,
                                                                      ),
                                                                      errorWidget: (context,
                                                                              url,
                                                                              error) =>
                                                                          Icon(Icons
                                                                              .error),
                                                                    ),
                                                                  ),
                                                            widget.selectedOption ==
                                                                    FsString
                                                                        .FASTAG_RECHARGE
                                                                ? Container()
                                                                : SizedBox(
                                                                    width: 20),
                                                            Expanded(
                                                                child:
                                                                    Container(
                                                              alignment: Alignment
                                                                  .centerLeft,
                                                              child: Column(
                                                                crossAxisAlignment:
                                                                    CrossAxisAlignment
                                                                        .start,
                                                                children: <Widget>[
                                                                  Text(
                                                                    widget.operatorDetail![
                                                                        'Operator Name'],
                                                                    style: Theme.of(
                                                                            context)
                                                                        .textTheme
                                                                        .titleSmall!
                                                                        .copyWith(
                                                                            color:
                                                                                Theme.of(context).colorScheme.onSurface),
                                                                    textAlign:
                                                                        TextAlign
                                                                            .left,
                                                                  ),
                                                                  Text(
                                                                    billersData![
                                                                        'cn'],
                                                                    style: Theme.of(
                                                                            context)
                                                                        .textTheme
                                                                        .bodySmall,
                                                                    textAlign:
                                                                        TextAlign
                                                                            .left,
                                                                  ),
                                                                ],
                                                              ),
                                                            )),
                                                          ],
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        height: 10.0,
                                                        child: Divider(
                                                            color: Theme.of(
                                                                    context)
                                                                .colorScheme
                                                                .onSurface
                                                                .withOpacity(
                                                                    0.2),
                                                            height: 2.0),
                                                      ),
                                                      Padding(
                                                        padding:
                                                            EdgeInsets.only(
                                                                top: 15.0,
                                                                left: 0,
                                                                right: 0,
                                                                bottom: 10.0),
                                                        child: Text(
                                                          "Biller Details",
                                                          style: Theme.of(
                                                                  context)
                                                              .textTheme
                                                              .bodyLarge!
                                                              .copyWith(
                                                                  color: Theme.of(
                                                                          context)
                                                                      .colorScheme
                                                                      .onSurface),
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                bottom: 15.0),
                                                        child: Table(
                                                          children: [
                                                            TableRow(children: [
                                                              billersData![
                                                                          'customerName'] ==
                                                                      null
                                                                  ? Container()
                                                                  : Text(
                                                                      billersData![
                                                                          'customerName'],
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                              billersData![
                                                                          'userName'] ==
                                                                      null
                                                                  ? Container()
                                                                  : Text(
                                                                      ': ${billersData!['userName']}',
                                                                      maxLines:
                                                                          2,
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                            ]),
                                                            // !widget.billersDetail.containsKey("billNumber")
                                                            //     ? Container()
                                                            //     :
                                                            TableRow(children: [
                                                              !widget.billersDetail!
                                                                      .containsKey(
                                                                          "billNumber")
                                                                  ? Container()
                                                                  : Text(
                                                                      "Bill Number",
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                              !widget.billersDetail!
                                                                      .containsKey(
                                                                          "billNumber")
                                                                  ? Container()
                                                                  : Text(
                                                                      ': ${billersData!['billNumber'].toString()}',
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                            ]),
                                                            TableRow(children: [
                                                              billersData!['billdate'] ==
                                                                          null ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .BROADBAND_RECHARGE
                                                                  ? Container()
                                                                  : Text(
                                                                      "Bill Date",
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                              billersData!['billdate'] ==
                                                                          null ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .BROADBAND_RECHARGE
                                                                  ? Container()
                                                                  : Text(
                                                                      ': ${billersData!['billdate']}',
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                            ]),
                                                            TableRow(children: [
                                                              billersData!['billAmount'] ==
                                                                          null ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .FASTAG_RECHARGE
                                                                  ? Container()
                                                                  : Text(
                                                                      widget.selectedOption ==
                                                                              FsString.BROADBAND_RECHARGE
                                                                          ? 'Bill Amount'
                                                                          : "Early Pay Amount",
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                              billersData!['billAmount'] ==
                                                                          null ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .FASTAG_RECHARGE
                                                                  ? Container()
                                                                  : Text(
                                                                      ": ₹ " +
                                                                          (((billersData!["billAmount"] is String)
                                                                                      ? double.parse(billersData!["billAmount"])
                                                                                      : (billersData!["billAmount"] is int)
                                                                                          ? (billersData!["billAmount"] as int).toDouble()
                                                                                          : billersData!["billAmount"] as double) +
                                                                                  10.00)
                                                                              .toString(),
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                            ]),
                                                            TableRow(children: [
                                                              billersData!['billdate'] ==
                                                                          null ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .BROADBAND_RECHARGE
                                                                  ? Container()
                                                                  : Text(
                                                                      "Early Pay Date",
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                              billersData!['billdate'] ==
                                                                          null ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .BROADBAND_RECHARGE
                                                                  ? Container()
                                                                  : Text(
                                                                      ': ${billersData!['billdate'].toString()}',
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                            ]),
                                                            TableRow(children: [
                                                              billersData!['billAmount'] ==
                                                                          null ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .GAS_BILL ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .BROADBAND_RECHARGE ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .FASTAG_RECHARGE
                                                                  ? Container()
                                                                  : Text(
                                                                      "After Due Date",
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                              TextStyle(height: 1.5)),
                                                                    ),
                                                              billersData!['billAmount'] ==
                                                                          null ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .GAS_BILL ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .BROADBAND_RECHARGE ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .EMI_PAYMENT ||
                                                                      widget.selectedOption ==
                                                                          FsString
                                                                              .FASTAG_RECHARGE
                                                                  ? Container()
                                                                  : Text(
                                                                      ": ₹ " +
                                                                          (double.parse(billersData!['billAmount']) + 10.00)
                                                                              .toString(),
                                                                      style: Theme.of(
                                                                              context)
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .merge(
                                                                            TextStyle(height: 1.5),
                                                                          ),
                                                                    ),
                                                            ]),
                                                          ],
                                                        ),
                                                      ),
                                                      SizedBox(height: 10),
                                                      Column(
                                                        children: <Widget>[
                                                          widget.selectedOption !=
                                                                  FsString
                                                                      .FASTAG_RECHARGE
                                                              ? Container()
                                                              : CustomTextField(
                                                                  inputFormatters: [
                                                                    FilteringTextInputFormatter
                                                                        .digitsOnly,
                                                                  ],
                                                                  keyboardType:
                                                                      TextInputType
                                                                          .number,
                                                                  onChanged:
                                                                      (value) {
                                                                    fastagRechargeAmount =
                                                                        value.replaceAll(
                                                                            ' ',
                                                                            '');
                                                                    fastagRechargeAmount =
                                                                        double.parse(fastagRechargeAmount)
                                                                            .toString();
                                                                  },
                                                                  hintText:
                                                                      'Amount',
                                                                  title:
                                                                      'Amount (Min ₹${(double.tryParse(billersData?['billnetamount']?.toString() ?? '0') ?? 0).toStringAsFixed(0)})',
                                                                ),
                                                          widget.selectedOption ==
                                                                  FsString
                                                                      .FASTAG_RECHARGE
                                                              ? Container()
                                                              : Container(
                                                                  alignment:
                                                                      Alignment
                                                                          .topLeft,
                                                                  child: Text(
                                                                    '₹ ' +
                                                                        billersData!['billnetamount']
                                                                            .toString(),
                                                                    style: Theme.of(
                                                                            context)
                                                                        .textTheme
                                                                        .headlineMedium,
                                                                  ),
                                                                ),
                                                          widget.selectedOption ==
                                                                  FsString
                                                                      .FASTAG_RECHARGE
                                                              ? Container()
                                                              : SizedBox(
                                                                  height: 10.0,
                                                                  child: Divider(
                                                                      color: Theme.of(
                                                                              context)
                                                                          .colorScheme
                                                                          .onSurface
                                                                          .withOpacity(
                                                                              0.2),
                                                                      height:
                                                                          2.0),
                                                                ),
                                                          SizedBox(height: 5),
                                                          billersData!['dueDate'] ==
                                                                      "null" ||
                                                                  billersData![
                                                                          'dueDate'] ==
                                                                      null ||
                                                                  widget.selectedOption ==
                                                                      FsString
                                                                          .FASTAG_RECHARGE ||
                                                                  widget.selectedOption ==
                                                                      FsString
                                                                          .BROADBAND_RECHARGE
                                                              ? Container()
                                                              : Container(
                                                                  alignment:
                                                                      Alignment
                                                                          .topLeft,
                                                                  child: Text(
                                                                    // '${DateFormat("dd-MM-yyyy").format(DateTime.parse(billersData['dueDate'])).toString()}',
                                                                    'Due Date: ${DateFormat("dd MMM yyyy").format(DateTime.parse(billersData!['dueDate'])).toString()}',

                                                                    style: Theme.of(
                                                                            context)
                                                                        .textTheme
                                                                        .bodyMedium!
                                                                        .merge(TextStyle(
                                                                            color:
                                                                                Theme.of(context).colorScheme.error)),
                                                                  ),
                                                                ),
                                                        ],
                                                      ),
                                                      SizedBox(height: 25.0),
                                                    ]),
                                              ),
                                            ],
                                          ),
                          ),
                floatingActionButton:
                    widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
                            widget.selectedOption == FsString.DTH_RECHARGE ||
                            widget.selectedOption == "datacard prepaid" ||
                            _loadError
                        ? Container(height: 0)
                        : Padding(
                            padding: EdgeInsets.fromLTRB(10.0, 10.0, 10.0, 5.0),
                            child: isLoading
                                ? Container(
                                    height: 10,
                                  )
                                : CustomLargeBtn(
                                    onPressed: () {
                                      _loadBillDetailsPage(tag: 'pay');
                                    },
                                    text: 'Pay',
                                  ),

                            // ElevatedButton(
                            //     style: ButtonStyle(
                            //         textStyle:
                            //             MaterialStateProperty.resolveWith(
                            //                 (states) => Theme.of(context)
                            //                     .textTheme
                            //                     .labelLarge),
                            //         backgroundColor:
                            //             MaterialStateProperty.resolveWith(
                            //                 (states) => Theme.of(context)
                            //                     .colorScheme
                            //                     .secondary)),
                            //     onPressed: () {
                            //       _loadBillDetailsPage(tag: 'pay');
                            //     },
                            //     child: Text('Pay'),
                            //   ),
                          ),
              ),
              loadingSuccessPage
                  ? Container(
                      color: Colors.white.withOpacity(0.7),
                      alignment: Alignment.center,
                      child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).primaryColor)),
                    )
                  : Container(),
            ]));
  }

  // onClickPayButton() {
  //   if (fastagRechargeAmount != null) {
  //     if (fastagRechargeAmount == null ||
  //         fastagRechargeAmount == "" ||
  //         double.parse(fastagRechargeAmount).toInt() < 200) {
  //       Toastly.error(context, 'Payable amount should be more than Rs. 200');
  //     } else if (double.parse(fastagRechargeAmount).toInt() > 100000) {
  //       Toastly.error(context, 'Payable amount should not exceed Rs. 1 lakh');
  //     } else {
  //       dataBeforeBillPayment();
  //     }
  //   } else {
  //     dataBeforeBillPayment();
  //   }
  // }
  onClickPayButton() {
    final billNetAmountRaw = billersData?['billnetamount'];
    final billNetAmount = billNetAmountRaw != null
        ? double.tryParse(billNetAmountRaw.toString()) ?? 0.0
        : 0.0;

    if (fastagRechargeAmount != null &&
        fastagRechargeAmount.toString().trim().isNotEmpty) {
      final enteredAmount =
          double.tryParse(fastagRechargeAmount.toString()) ?? 0.0;

      // Validation: minimum must be at least equal to billnetamount
      if (enteredAmount < billNetAmount) {
        Toastly.error(context,
            'Payable amount should be at least ₹ ${billNetAmount.toStringAsFixed(2)}');
      } else if (enteredAmount > 100000) {
        Toastly.error(context, 'Payable amount should not exceed ₹ 1,00,000');
      } else {
        dataBeforeBillPayment();
      }
    } else {
      dataBeforeBillPayment();
      //Toastly.error(context, 'Please enter an amount to proceed.');
    }
  }

  void _loadBillDetailsPage({tag}) async {
    AppUtils.checkInternetConnection().then((value) {
      if (value) {
        _loadError = false;
        _loadErrorType = null;
        _loadErrorMsg = null;
        if (tag == 'pay') {
          onClickPayButton();
        } else {
          getDetail();
        }
      } else {
        _loadError = true;
        _loadErrorMsg = FsString.ERROR_NO_INTERNET_RETRY;
        _loadErrorType = LoadingErrorType.INTERNET;
      }
      setState(() {});
    });
  }

  Widget _buildErrorWidget() {
    return Center(
      child: WidgetUtils.getErrorWidget(
          module: AppConstant.ONE_PAY,
          errorMsg: _loadErrorMsg!,
          errorType: _loadErrorType,
          showErrorIcon: true,
          shouldRetry: LoadingErrorUtils.canRetry(_loadErrorType),
          onRetryPressed: () {
            _loadBillDetailsPage();
          },
          retryButtonColor: Theme.of(context).primaryColor),
    );
  }

  Widget _getShimmeringListLoaderWidget() {
    return GroceryUiUtils.billStructureLoader();
  }

  functionCall() {
    if (widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
        widget.selectedOption == FsString.DTH_RECHARGE ||
        widget.selectedOption == "datacard prepaid" ||
        (widget.billersDetail!['viewBill'].toLowerCase() == 'not required')) {
      if (widget.action ==
          "recharge amt") // to open direct razorpay recharge page
      {
        fastagRechargeAmount = widget.amount.toString();
        rechargeAmount = widget.amount;
        dataBeforeBillPayment(rechargeAmt: rechargeAmount);
        // payBill();
      } else if (widget.action == "view plans") {
        // widget.selectedOption = FsString.PREPAID_MOBILE_RECHARGE;
        viewRechargePlan(searchTxt: "");
      } else {
        viewRechargePlan(searchTxt: ""); // open data plan page
      }
    } else {
      widget.action = null;
      viewBill();
    }
  }

  dataToSave({rechargeAmt}) {
    setState(() {
      rechargeAmount = rechargeAmt;
    });
    HashMap<String, String?> h = new HashMap();
    h["type"] = widget.selectedOption;
    h["cirId"] = widget.billersDetail!['cirId'].toString();
    h["ad1"] = widget.billersDetail!['ad1'].toString();
    h["ad2"] = widget.billersDetail!['ad2'].toString();
    h["ad3"] = widget.billersDetail!['ad3'].toString();
    h["op_name"] = widget.operatorDetail!['Operator Name'].toString();
    h["amt"] = fastagRechargeAmount != null
        ? fastagRechargeAmount
        : widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
                widget.selectedOption == FsString.DTH_RECHARGE ||
                widget.selectedOption == "datacard prepaid"
            ? rechargeAmt.toString()
            : billersData!['billnetamount'].toString();
    h["cn"] = widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
            widget.selectedOption == FsString.DTH_RECHARGE ||
            widget.selectedOption == "datacard prepaid" ||
            (widget.billersDetail!['viewBill'].toLowerCase() == 'not required')
        ? widget.mobileNumber.toString()
        : billersData!['cn'].toString();
    h["op"] = widget.operatorDetail!['Operator Id'].toString();
    // return {
    //   //"billerDetail": billersData,
    //   "cirId": widget.billersDetail['cirId'].toString(),
    //   "op_name": widget.operatorDetail['Operator Name'].toString(),
    //   "amt": widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
    //           widget.selectedOption == FsString.DTH_RECHARGE
    //       ? rechargeAmount
    //       : billersData['billnetamount'].toString(),
    //   "cn": widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
    //           widget.selectedOption == FsString.DTH_RECHARGE
    //       ? widget.mobileNumber
    //       : billersData['cellNumber'].toString(),
    //   "op": widget.operatorDetail['Operator Id'].toString(),
    // };

    return h;
  }

  // dataToSave() {
  //   return {
  //     "billerDetail": billersData,
  //     "operatorDetail": widget.billersDetail,
  //     "op_name": widget.operatorDetail['Operator Name'].toString(),
  //     "amt": widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
  //             widget.selectedOption == FsString.DTH_RECHARGE
  //         ? rechargeAmount
  //         : billersData['billnetamount'].toString(),
  //     "cn": widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
  //             widget.selectedOption == FsString.DTH_RECHARGE
  //         ? widget.mobileNumber
  //         : billersData['cellNumber'].toString(),
  //     "op": widget.operatorDetail['Operator Id'].toString(),
  //   };
  // }

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    //Fluttertoast.showToast(msg: "SUCCESS: " + response.paymentId);
    setState(() {
      loadingSuccessPage = true;
    });

    // Directly proceed with bill payment API call
    // No timeout dialog - let the API response determine the outcome

    billPaymentApi(
        order_id: billersData!['order_id'],
        billerDetail: billersData,
        operatorDetail: widget.operatorDetail,
        op_name: widget.operatorDetail!['Operator Name'].toString(),
        amt: fastagRechargeAmount != null
            ? fastagRechargeAmount
            : widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
                    widget.selectedOption == FsString.DTH_RECHARGE ||
                    widget.selectedOption == "datacard prepaid"
                ? rechargeAmount
                : billersData!['billnetamount'].toString(),
        cn: widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
                widget.selectedOption == FsString.DTH_RECHARGE ||
                widget.selectedOption == "datacard prepaid" ||
                (widget.billersDetail!['viewBill'].toLowerCase() ==
                    'not required')
            ? widget.mobileNumber
            : billersData!['cellNumber'].toString(),
        op: widget.operatorDetail!['Operator Id'].toString(),
        pId: response.paymentId,
        razorpayOrderId: response.orderId,
        razorpaySignature: response.signature);
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    // Extract error details from Razorpay response
    String errorCode = response.code?.toString() ?? "UNKNOWN";
    String errorMessage =
        response.message ?? "Payment failed due to unknown error";

    // Log error for debugging
    print("Payment Error - Code: $errorCode, Message: $errorMessage");

    // Show detailed error message
    Toastly.error(context, "Payment Failed: $errorMessage");

    // Use the same comprehensive failure data structure
    _navigateToFailureScreen("Razorpay Error ($errorCode): $errorMessage");

    // Handle specific navigation for certain payment types
    if (widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
        widget.selectedOption == "datacard prepaid" ||
        widget.selectedOption == FsString.DTH_RECHARGE ||
        (widget.billersDetail != null &&
            widget.billersDetail!['viewBill']?.toString().toLowerCase() ==
                'not required')) {
      Navigator.pop(context, context);
    }
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    Fluttertoast.showToast(msg: "EXTERNAL_WALLET: " + response.walletName!);
  }

  void _navigateToFailureScreen(String errorMessage) {
    // Ensure all required data is available with null safety
    String safeSelectedOption = widget.selectedOption ?? "Unknown";
    Map<String, dynamic> safeBillersDetail =
        Map<String, dynamic>.from(billersDetail ?? {});
    Map<String, dynamic> safeOpDetail =
        Map<String, dynamic>.from(op_Detail ?? {});
    Map<String, dynamic> safeBillersData =
        Map<String, dynamic>.from(billersData ?? {});

    // Create comprehensive failure transaction data with all required fields
    Map<String, dynamic> failureData = {
      "reqid":
          "FAILED_${DateTime.now().millisecondsSinceEpoch}", // Required for transaction ID display
      "billAmount": safeBillersData['billnetamount']?.toString() ??
          "0", // Required for amount display
      "cn": safeBillersDetail['cellNumber']?.toString() ??
          safeBillersDetail['consumerNumber']?.toString() ??
          widget.mobileNumber?.toString() ??
          "N/A", // Required for customer number display
      "rechargeDetails": {
        "status": "FAILED",
        "error_message": errorMessage,
        "transaction_id": "N/A",
        "amount": safeBillersData['billnetamount']?.toString() ?? "0",
        "timestamp": DateTime.now().toString(),
        "opRefNo": null, // BBPS transaction ID (null for failed transactions)
      },
      "paymentDetails": {
        "amount": (double.tryParse(
                    safeBillersData['billnetamount']?.toString() ?? "0") ??
                0.0) *
            100, // Amount in paise
        "method": "razorpay",
        "status": "FAILED"
      }
    };

    // Navigate directly to failure screen with comprehensive data
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => BillPaymentSuccussful(
          selectedOtion: safeSelectedOption,
          customerDetail: safeBillersDetail,
          operatorsDetail: safeOpDetail,
          paymentStatus: "FAILED",
          paymentData: failureData,
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _razorpay.clear();
  }

  dataBeforeBillPayment({rechargeAmt}) {
    mobikwikPresenter.saveDataBeforePayment(
        callingType: "saveBillData",
        billData: dataToSave(rechargeAmt: rechargeAmt));
  }

  viewBill() {
    mobikwikPresenter.viewBill(
      callingType: "viewBill",
      operatorId: widget.operatorDetail!['Operator Id'].toString(),
      billParams: widget.billersDetail,
    );
  }

  viewRechargePlan({searchTxt}) {
    mobikwikPresenter.viewPlan(
      circleId: widget.billersDetail!['cirId'],
      callingType: "viewRechargePlan",
      searchText: searchTxt,
      operatorId: widget.operatorDetail!['Operator Id'].toString(),
    );
  }

  Map? billersData = {};

  @override
  error(error, {callingType}) {
    if (callingType == "viewBill") {
      Navigator.pop(context);
      // Toastly.error(context, 'Invalid customer account.');
      print("error:" + error.toString());
    } else if (callingType == "viewRechargePlan") {
      print("error:" + error.toString());
    } else if (callingType == "billPayment") {
      // Reset loading state and navigate directly to failure screen
      setState(() {
        loadingSuccessPage = false;
      });

      // Navigate directly to failure screen with error details
      _navigateToFailureScreen(error.toString());
      print("billPayment error:" + error.toString());
    }
  }

  @override
  failure(failed, {callingType}) {
    if (callingType == "viewBill") {
      print("failure:" + failed.toString());
    } else if (callingType == "viewRechargePlan") {
      print("failure:" + failed.toString());
    } else if (callingType == "billPayment") {
      // Reset loading state and navigate directly to failure screen
      setState(() {
        loadingSuccessPage = false;
      });

      // Navigate directly to failure screen with failure details
      _navigateToFailureScreen(failed.toString());
      print("billPayment failure:" + failed.toString());
    }
  }

  var rechargeAmount;

  payBill({rechargeAmt}) {
    var currentConfig = Environment().getCurrentConfig();
    var options = {
      'key': currentConfig.razorpayApiKey,
      'order_id': billersData!['order_id'],
      'amount': fastagRechargeAmount != null
          ? fastagRechargeAmount
          : widget.selectedOption == FsString.PREPAID_MOBILE_RECHARGE ||
                  widget.selectedOption == FsString.DTH_RECHARGE ||
                  widget.selectedOption == "datacard prepaid"
              ? rechargeAmount * 100
              : double.parse(billersData!['billnetamount']) * 100,
      'name': name,
      'description': desc,
      'options': {
        'checkout': {
          'method': {'netbanking': '1', 'card': '1', 'upi': '1', 'wallet': '1'},
        },
      },
      'prefill': {'contact': mobile, 'email': email}
    };
    // dataBeforeBillPayment();
    print("options::" + options.toString());
    try {
      _razorpay.open(options);
    } catch (e) {
      print(e);
    }
    setState(() {});
  }

/////////////////////////
  Map? billersDetail = {};
  Map? op_Detail = {};

  billPaymentApi(
      {order_id,
      op_name,
      amt,
      cn,
      op,
      pId,
      billerDetail,
      operatorDetail,
      razorpayOrderId,
      razorpaySignature}) {
    setState(() {
      billersDetail = billerDetail;
      op_Detail = operatorDetail;
    });
    mobikwikPresenter.billPayment(
        callingType: "billPayment",
        type: widget.selectedOption,
        order_id: order_id,
        //"electricity",
        op_name: op_name,
        amount: amt,
        operatorid: op,
        cn: cn,
        paymentId: pId,
        razorpayOrderId: razorpayOrderId,
        razorpaySignature: razorpaySignature);
  }

  paymentStatus(paymentStatus, paymentInfo) {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => BillPaymentSuccussful(
                  selectedOtion: widget.selectedOption,
                  customerDetail: billersDetail,
                  operatorsDetail: op_Detail,
                  paymentStatus: paymentStatus,
                  paymentData: paymentInfo,
                )));
  }

  List? rechargePlans = [];

  /// Handles all Mobikwik presenter callbacks safely.
  @override
  success(
    dynamic success, {
    dynamic callingType,
    String? searchedText,
  }) {
    // `callingType` can be null or any type, so normalise it to a String.
    final String callType = callingType?.toString() ?? '';

    switch (callType) {
      // ───────────────────────────────────────────── viewBill ──────────────
      case 'viewBill':
        final data = success['data'];

        if (data == null) {
          final msg = success['message']?['text'] ?? 'Unable to fetch bill.';
          if (mounted) {
            Navigator.pop(context);
            Toastly.error(context, msg);
          }
          return;
        }

        // Accept both a single-object Map and a List of Maps.
        if (data is Map) {
          billersData = Map<String, dynamic>.from(data);
        } else if (data is List && data.isNotEmpty) {
          billersData = Map<String, dynamic>.from(data.first as Map);
        } else {
          Toastly.error(context, 'Unexpected bill data structure.');
          return;
        }

        /* Support both old (`billnetamount`) and new (`billAmount`) keys */
        final billAmount =
            billersData!['billnetamount'] ?? billersData!['billAmount'];

        final isZeroOrNull = billAmount == null ||
            billAmount.toString() == 'null' ||
            billAmount.toString() == '0.0' ||
            billAmount == 0 ||
            billAmount == 0.0;

        if (isZeroOrNull) {
          if (mounted) {
            Navigator.pop(context);
            Toastly.error(context, 'No pending dues.');
          }
        } else {
          if (mounted) setState(() => isLoading = false);
        }

        debugPrint('view bill res: $billersData');
        break;

      // ─────────────────────────────────────── viewRechargePlan ────────────
      case 'viewRechargePlan':
        final plans = success['data']?['plans'] as List<dynamic>? ?? [];
        if (plans.isNotEmpty) {
          rechargePlans = plans;
          if (mounted) {
            setState(() => isLoading = false);
            viewBillKey.currentState?.setListElement(dataList: rechargePlans);
          }
          debugPrint('plans: $rechargePlans');
        }
        break;

      // ─────────────────────────────────────────── billPayment ─────────────
      case 'billPayment':
        // Reset loading state first
        setState(() {
          loadingSuccessPage = false;
        });

        final data = success['data'];
        if (data != null) {
          debugPrint('billpayment: $success');
          paymentStatus(data['status'], data);
        } else {
          // Handle case where data is null
          Toastly.error(context,
              "Payment confirmation failed. Please check your transaction history.");
        }
        break;

      // ─────────────────────────────────────────── default / unknown ───────
      default:
        debugPrint('Unhandled callingType: $callType');
    }
  }

  @override
  orderfailure(failed, {callingType}) {
    // Toastly.error(context, "Please try again later.");
    Toastly.error(context, failed.toString());
  }

  @override
  ordererror(error, {callingType}) {
    Toastly.error(context, "Please try again later.");
  }

// @override
// ordererror(error, {callingType}) {
//   debugPrint("ordererror: $error");
//   debugPrint("ordererror type: ${error.runtimeType}");

//   try {
//     if (error == null) {
//       Toastly.error(context, "An unknown error occurred.");
//       return;
//     }

//     Map<String, dynamic>? parsedError;

//     if (error is String) {
//       // JSON-style error string
//       if (error.trim().startsWith('{') && error.trim().endsWith('}')) {
//         parsedError = jsonDecode(error) as Map<String, dynamic>;
//       } else {
//         // Non-JSON error string (like NoSuchMethodError)
//         Toastly.error(context, "Something went wrong. Please try again.");
//         return;
//       }
//     } else if (error is Map<String, dynamic>) {
//       parsedError = error;
//     }

//     if (parsedError != null) {
//       final errors = parsedError['errors'];
//       if (errors is Map<String, dynamic> && errors.isNotEmpty) {
//         final firstKey = errors.keys.first;
//         final firstMessage = errors[firstKey];
//         if (firstMessage is List && firstMessage.isNotEmpty) {
//           Toastly.error(context, firstMessage.first.toString());
//           return;
//         }
//       }

//       if (parsedError.containsKey('message') && parsedError['message'] != null) {
//         Toastly.error(context, parsedError['message'].toString());
//         return;
//       }
//     }

//     Toastly.error(context, "Please try again later.");
//   } catch (e) {
//     debugPrint("ordererror parsing failed: $e");
//     Toastly.error(context, "An unexpected error occurred.");
//   }
// }

  @override
  ordersuccess(success, {callingType, String? searchedText}) {
    if (success != null) {
      setState(() {
        billersData!['order_id'] = success['data']['order_id'];
      });
    }
    payBill();
  }
}
