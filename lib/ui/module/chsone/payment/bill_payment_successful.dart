import 'package:cached_network_image/cached_network_image.dart';
import 'package:common_config/utils/toast/toast.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sso_futurescape/config/colors/color.dart';
import 'package:sso_futurescape/config/strings/strings.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/custom_widgets/b_assured_logo_widget.dart';
import 'package:sso_futurescape/services/sonic_branding_service.dart';
import 'package:sso_futurescape/theme/theme.dart';
import 'package:sso_futurescape/ui/module/dashboard/new/ui/dashboard/main_dashboard.dart';
import 'package:sso_futurescape/ui/module/help_support.dart';
import 'package:sso_futurescape/ui/widgets/my_flutter_app_icons.dart';

// ignore: must_be_immutable
class BillPaymentSuccussful extends StatefulWidget {
  var paymentStatus;
  var customerDetail;
  var operatorsDetail;
  Map? paymentData;
  var selectedOtion;
  BillPaymentSuccussful({
    key = Key,
    this.paymentData,
    this.customerDetail,
    this.operatorsDetail,
    this.paymentStatus,
    this.selectedOtion,
  });
  @override
  _BillPaymentSuccussfulState createState() => _BillPaymentSuccussfulState();
}

class _BillPaymentSuccussfulState extends State<BillPaymentSuccussful> {
  // ignore: unused_field
  final TextEditingController _searchControl = new TextEditingController();

  @override
  void initState() {
    super.initState();
    paymentDetails();
    // Trigger sonic branding for successful bill payments
    _playSonicBranding();
  }

  Future<void> _playSonicBranding() async {
    try {
      await SonicBrandingService.playPaymentSuccessSound();
    } catch (e) {
      print('Error playing sonic branding: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        // backClick(context);
        Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => MainDashboardV2()),
            (Route<dynamic> route) => true);
        return true;
      },
      child: MyScrollView(
        backBtnColor: Colors.white,
        pageTitle: widget.paymentData!['rechargeDetails']['status'] == "SUCCESS"
            // widget.paymentStatus == "SUCCESS"
            ? 'Bill Payment Successfully'
            : widget.paymentData!['rechargeDetails']['status'].toUpperCase() ==
                    "SUCCESSPENDING"
                ? "Transaction Pending"
                : "Transaction Failed",
        pageTitleColor: Colors.white,
        appBarColor: widget.paymentData!['rechargeDetails']['status'] ==
                "SUCCESS"
            ? Color(0xFF29A329)
            : widget.paymentData!['rechargeDetails']['status'].toUpperCase() ==
                    "SUCCESSPENDING"
                ? Color.fromRGBO(255, 193, 7, 1.0)
                : kPrimaryRedColor,
        backButtonPressed: () {
          Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(builder: (context) => MainDashboardV2()),
              (Route<dynamic> route) => true);
        },
        actions: [
          BAssuredLogo(height: 50),
        ],
        // pageTitleWidget: Column(
        //   mainAxisAlignment: MainAxisAlignment.center,
        //   crossAxisAlignment: CrossAxisAlignment.start,
        //   children: [
        //     Text(
        //       widget.paymentData!['rechargeDetails']['status'] == "SUCCESS"
        //           // widget.paymentStatus == "SUCCESS"
        //           ? 'Bill Payment Successful'
        //           : widget.paymentData!['rechargeDetails']['status']
        //                       .toUpperCase() ==
        //                   "SUCCESSPENDING"
        //               ? "Transaction Pending"
        //               : "Transaction Failed",
        //       style: TextStyle(

        //         color: Colors.white,
        //       ),
        //     ),
        //     Text(
        //       // '01:21PM on 31 Jan 2021',
        //       '${DateFormat("hh : mm a on dd MMM yyyy").format(DateTime.now()).toString()}',
        //       style: Theme.of(context).textTheme.bodyMedium!.merge(TextStyle(
        //             fontWeight: FontWeight.w500,
        //             color: Colors.white,
        //           )),
        //     )
        //   ],
        // ),
        pageBody: SingleChildScrollView(
          child: Column(
            children: <Widget>[
              Container(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // widget.paymentStatus == "SUCCESS"
                      widget.paymentData!['rechargeDetails']['status'] ==
                              "SUCCESS"
                          ? Container()
                          : Container(
                              padding:
                                  EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 20.0),
                              alignment: Alignment.topLeft,
                              child: Text(
                                widget.paymentData!['rechargeDetails']['status']
                                            .toUpperCase() ==
                                        "SUCCESSPENDING"
                                    ? "Recharge is pending. Please check after sometime. Your amount will be refunded if the recharge fails. "
                                    : "If your order has failed, any amount debited will be auto-refunded in 5 working days (not taking into account the processing time of your bank).",

                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium!
                                    .merge(TextStyle(
                                      color:
                                          widget.paymentData!['rechargeDetails']
                                                          ['status']
                                                      .toUpperCase() ==
                                                  "SUCCESSPENDING"
                                              ? Color.fromRGBO(255, 193, 7, 1.0)
                                              : Theme.of(context)
                                                  .colorScheme
                                                  .error,
                                    )),
                                // style: TextStyle(
                                //   fontFamily: 'Gilroy-Regular',
                                //   fontSize: FSTextStyle.h5size,
                                //   color: widget.paymentData['rechargeDetails']
                                //                   ['status']
                                //               .toUpperCase() ==
                                //           "SUCCESSPENDING"
                                //       ? Color.fromRGBO(255, 193, 7, 1.0)
                                //       : FsColor.red,
                                // ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                      // widget.paymentStatus == "SUCCESS"
                      widget.paymentData!['rechargeDetails']['status'] ==
                              "SUCCESS"
                          ? Container()
                          : SizedBox(
                              height: 10.0,
                              child: Divider(
                                  color: Theme.of(context).dividerColor,
                                  height: 2.0),
                            ),
                      Container(
                        padding: EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 20.0),
                        alignment: Alignment.topLeft,
                        child: Row(
                          children: <Widget>[
                            Container(
                              alignment: Alignment.centerLeft,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Text(
                                    'Transaction ID',
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                    textAlign: TextAlign.left,
                                  ),
                                  SizedBox(
                                    height: 10.0,
                                  ),
                                  Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Text(
                                          widget.paymentData?['reqid']
                                                  ?.toString() ??
                                              'N/A',
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleSmall,
                                          textAlign: TextAlign.left,
                                        ),
                                      ]),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Container(
                                alignment: Alignment.centerRight,
                                child: TextButton(
                                  child: Text(
                                    'Copy',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall!
                                        .merge(
                                            TextStyle(color: kPrimaryRedColor)),
                                    textAlign: TextAlign.right,
                                  ),
                                  onPressed: () {
                                    // FlutterClipboard.copy('hello flutter friends').then(( value ) => print('copied'));
                                    Clipboard.setData(new ClipboardData(
                                        text: widget.paymentData?['reqid']
                                                ?.toString() ??
                                            'N/A'));
                                    Toastly.success(
                                        context, 'Transaction ID is copied');
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10.0,
                        child: Divider(
                            color: Theme.of(context).dividerColor, height: 2.0),
                      ),
                      widget.paymentData!['rechargeDetails']['status'] !=
                                  "SUCCESS" ||
                              widget.paymentData!['rechargeDetails']
                                      ['opRefNo'] ==
                                  null.toString() ||
                              widget.paymentData!['rechargeDetails']
                                      ['opRefNo'] ==
                                  null
                          ? Container()
                          : Container(
                              padding:
                                  EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 20.0),
                              alignment: Alignment.topLeft,
                              child: Row(
                                children: <Widget>[
                                  Container(
                                    alignment: Alignment.centerLeft,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Text(
                                          'BBPS Transaction ID',
                                          style: TextStyle(
                                            fontFamily: 'Gilroy-SemiBold',
                                            fontSize: FSTextStyle.h5size,
                                            color: FsColor.darkgrey,
                                          ),
                                          textAlign: TextAlign.left,
                                        ),
                                        SizedBox(
                                          height: 10.0,
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: <Widget>[
                                            Text(
                                              widget.paymentData?[
                                                          'rechargeDetails']
                                                          ['opRefNo']
                                                      ?.toString() ??
                                                  '1',
                                              style: TextStyle(
                                                fontFamily: 'Gilroy-SemiBold',
                                                fontSize: 14,
                                                color: FsColor.black,
                                              ),
                                              textAlign: TextAlign.left,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(
                                      alignment: Alignment.centerRight,
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(5),
                                        child: CachedNetworkImage(
                                          imageUrl:
                                              "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/B_Assured_Logo_PNG_fcf622d3fc.png",
                                          placeholder: (context, url) =>
                                              CircularProgressIndicator(),
                                          errorWidget: (context, url, error) =>
                                              Icon(Icons.error),
                                          height: 50,
                                        ),

                                        // Image.asset(
                                        //   'images/b_assured.png',
                                        //   height: 30,
                                        //   width: 30,
                                        //   fit: BoxFit.cover,
                                        // ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                      widget.paymentData!['rechargeDetails']['status'] !=
                                  "SUCCESS" ||
                              widget.paymentData!['rechargeDetails']
                                      ['opRefNo'] ==
                                  null.toString() ||
                              widget.paymentData!['rechargeDetails']
                                      ['opRefNo'] ==
                                  null
                          ? Container()
                          : SizedBox(
                              height: 10.0,
                              child: Divider(
                                  color: Theme.of(context).dividerColor,
                                  height: 2.0),
                            ),
                      // widget.paymentStatus != "SUCCESS"
                      widget.paymentData!['rechargeDetails']['status'] !=
                              "SUCCESS"
                          ? Container()
                          : Container(
                              padding:
                                  EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 20.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            widget.selectedOtion ==
                                                        FsString
                                                            .PREPAID_MOBILE_RECHARGE ||
                                                    widget.selectedOtion ==
                                                        "datacard prepaid"
                                                ? "Mobile Recharge Paid"
                                                : widget.selectedOtion +
                                                    " Bill Paid",
                                            style: TextStyle(
                                              fontSize: FSTextStyle.h5size,
                                              fontFamily: 'Gilroy-SemiBold',
                                              color: FsColor.darkgrey,
                                              height: 1,
                                            ),
                                            textAlign: TextAlign.left,
                                          ),
                                          SizedBox(
                                            height: 15.0,
                                          ),
                                          Row(
                                            children: [
                                              Expanded(
                                                  child: Container(
                                                // color: Colors.amber,
                                                padding: EdgeInsets.only(
                                                    top: 0.0,
                                                    left: 0,
                                                    right: 0,
                                                    bottom: 10.0),
                                                alignment: Alignment.topLeft,
                                                child: Row(
                                                  children: <Widget>[
                                                    widget.selectedOtion ==
                                                            FsString
                                                                .FASTAG_RECHARGE
                                                        ? Container()
                                                        : ClipRRect(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        5),
                                                            child:
                                                                CachedNetworkImage(
                                                              height: 48,
                                                              width: 48,
                                                              fit: BoxFit.cover,
                                                              imageUrl:
                                                                  'https://static.mobikwik.com/appdata/operator_icons/op' +
                                                                      '${widget.operatorsDetail["Operator Id"].toString()}' +
                                                                      '.png',
                                                              placeholder:
                                                                  (context,
                                                                          url) =>
                                                                      Image
                                                                          .asset(
                                                                'assets/img/loading.gif',
                                                                fit: BoxFit
                                                                    .cover,
                                                                width: 48,
                                                                height: 48,
                                                              ),
                                                              errorWidget: (context,
                                                                      url,
                                                                      error) =>
                                                                  Icon(Icons
                                                                      .error),
                                                            ),
                                                          ),
                                                    widget.selectedOtion ==
                                                            FsString
                                                                .FASTAG_RECHARGE
                                                        ? Container()
                                                        : SizedBox(width: 20),
                                                    Expanded(
                                                        child: Container(
                                                      alignment:
                                                          Alignment.centerLeft,
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: <Widget>[
                                                          Text(
                                                            widget.operatorsDetail[
                                                                'Operator Name'],
                                                            style: TextStyle(
                                                              fontFamily:
                                                                  'Gilroy-SemiBold',
                                                              fontSize:
                                                                  FSTextStyle
                                                                      .h5size,
                                                              color:
                                                                  FsColor.black,
                                                            ),
                                                            textAlign:
                                                                TextAlign.left,
                                                          ),
                                                          SizedBox(height: 3),
                                                          // widget.selectedOtion == FsString.PREPAID_MOBILE_RECHARGE ||
                                                          widget.selectedOtion ==
                                                                  "datacard prepaid"
                                                              ? Container()
                                                              : Text(
                                                                  widget.customerDetail !=
                                                                              null &&
                                                                          widget.customerDetail!.containsKey(
                                                                              'cellNumber')
                                                                      ? (widget.customerDetail!['cellNumber']
                                                                              ?.toString() ??
                                                                          'N/A')
                                                                      : (widget
                                                                              .paymentData?['cn']
                                                                              ?.toString() ??
                                                                          'N/A'),
                                                                  style:
                                                                      TextStyle(
                                                                    fontFamily:
                                                                        'Gilroy-SemiBold',
                                                                    fontSize:
                                                                        14,
                                                                    color: FsColor
                                                                        .lightgrey,
                                                                  ),
                                                                  textAlign:
                                                                      TextAlign
                                                                          .left,
                                                                ),
                                                        ],
                                                      ),
                                                    )),
                                                  ],
                                                ),
                                              )),
                                              // Expanded(

                                              //   child:
                                              Container(
                                                // color: Colors.red,
                                                alignment:
                                                    Alignment.centerRight,
                                                child: Text(
                                                  '₹${widget.paymentData?['billAmount']?.toString() ?? '0'}',

                                                  // '₹${(widget.paymentData['paymentDetails']['amount'] / 100).toString()}',
                                                  style: TextStyle(
                                                      fontFamily: 'Gilroy-Bold',
                                                      fontSize: 16,
                                                      color: FsColor.black),
                                                  textAlign: TextAlign.right,
                                                ),
                                              ),
                                              // ),
                                            ],
                                          ),
                                        ]),
                                  ),
                                  widget.selectedOtion ==
                                              FsString
                                                  .PREPAID_MOBILE_RECHARGE ||
                                          widget.selectedOtion ==
                                              "datacard prepaid" ||
                                          widget.selectedOtion ==
                                              FsString.DTH_RECHARGE ||
                                          widget.selectedOtion ==
                                              FsString.POSTPAID_MOBILE_RECHARGE
                                      ? Container()
                                      : Padding(
                                          padding: EdgeInsets.only(
                                              top: 15.0,
                                              left: 0,
                                              right: 0,
                                              bottom: 10.0),
                                          child: Text(
                                            "Billers Details",
                                            style: TextStyle(
                                              fontSize: FSTextStyle.h5size,
                                              fontFamily: 'Gilroy-SemiBold',
                                              color: FsColor.darkgrey,
                                              height: 1,
                                            ),
                                          ),
                                        ),
                                  widget.selectedOtion ==
                                              FsString
                                                  .PREPAID_MOBILE_RECHARGE ||
                                          widget.selectedOtion ==
                                              "datacard prepaid" ||
                                          widget.selectedOtion ==
                                              FsString.DTH_RECHARGE
                                      ? Container()
                                      : Padding(
                                          padding: const EdgeInsets.only(
                                              bottom: 0.0),
                                          child: Table(
                                            children: [
                                              TableRow(children: [
                                                widget.customerDetail[
                                                            'userName'] ==
                                                        null
                                                    ? Container()
                                                    : Text(
                                                        "Customer Name",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                                widget.customerDetail[
                                                            'userName'] ==
                                                        null
                                                    ? Container()
                                                    : Text(
                                                        ": ${widget.customerDetail['userName']}",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                              ]),
                                              TableRow(children: [
                                                !widget.customerDetail
                                                        .containsKey(
                                                            "billNumber")
                                                    ? Container()
                                                    : Text(
                                                        "Bill Number",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                                !widget.customerDetail
                                                        .containsKey(
                                                            "billNumber")
                                                    ? Container()
                                                    : Text(
                                                        ": 100002563859",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                              ]),
                                              TableRow(children: [
                                                widget.customerDetail[
                                                            'billdate'] ==
                                                        null
                                                    ? Container()
                                                    : Text(
                                                        "Bill Date",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                                widget.customerDetail[
                                                            'billdate'] ==
                                                        null
                                                    ? Container()
                                                    : Text(
                                                        ": ${widget.customerDetail['billdate']}",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                              ]),
                                              TableRow(children: [
                                                widget.selectedOtion ==
                                                            FsString
                                                                .FASTAG_RECHARGE ||
                                                        widget.customerDetail[
                                                                'billAmount'] ==
                                                            null
                                                    ? Container()
                                                    : Text(
                                                        "Early Pay Amount",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                                widget.selectedOtion ==
                                                            FsString
                                                                .FASTAG_RECHARGE ||
                                                        widget.customerDetail[
                                                                'billAmount'] ==
                                                            null
                                                    ? Container()
                                                    : Text(
                                                        ": ₹ ${widget.customerDetail['billAmount'].toString()}",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                              ]),
                                              TableRow(children: [
                                                widget.customerDetail[
                                                            'billdate'] ==
                                                        null
                                                    ? Container()
                                                    : Text(
                                                        "Early Pay Date",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                                widget.customerDetail[
                                                            'billdate'] ==
                                                        null
                                                    ? Container()
                                                    : Text(
                                                        ": ${widget.customerDetail['billdate']}",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                              ]),
                                              TableRow(children: [
                                                Text(
                                                  "Convenience Fee",
                                                  style: TextStyle(
                                                      fontFamily:
                                                          'Gilroy-Regular',
                                                      height: 1.5),
                                                ),
                                                Text(
                                                  ": ₹ 0.00", // Rupees symbol
                                                  style: TextStyle(
                                                    fontFamily:
                                                        'Gilroy-Regular',
                                                    height: 1.5,
                                                  ),
                                                )
                                              ]),
                                              TableRow(children: [
                                                widget.selectedOtion ==
                                                            FsString.GAS_BILL ||
                                                        widget.selectedOtion ==
                                                            FsString
                                                                .FASTAG_RECHARGE ||
                                                        widget.selectedOtion ==
                                                            FsString
                                                                .POSTPAID_MOBILE_RECHARGE
                                                    ? Container()
                                                    : Text(
                                                        "After Due Date",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                                widget.selectedOtion ==
                                                            FsString.GAS_BILL ||
                                                        widget.selectedOtion ==
                                                            FsString
                                                                .FASTAG_RECHARGE ||
                                                        widget.selectedOtion ==
                                                            FsString
                                                                .POSTPAID_MOBILE_RECHARGE
                                                    ? Container()
                                                    : Text(
                                                        ": ₹ ${(double.parse(widget.customerDetail['billAmount'].toString()) + 10.00).toString()}",
                                                        style: TextStyle(
                                                            fontFamily:
                                                                'Gilroy-Regular',
                                                            height: 1.5),
                                                      ),
                                              ]),
                                            ],
                                          ),
                                        ),
                                ],
                              ),
                            ),
                      // widget.paymentStatus != "SUCCESS"
                      (widget.paymentData?['rechargeDetails']?['status']
                                      ?.toString() ??
                                  "UNKNOWN") !=
                              "SUCCESS"
                          ? Container()
                          : SizedBox(
                              height: 10.0,
                              child: Divider(
                                  color: Theme.of(context).dividerColor,
                                  height: 2.0),
                            ),

                      Container(
                        padding: EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Debited From",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium,
                                      textAlign: TextAlign.left,
                                    ),
                                    SizedBox(
                                      height: 15.0,
                                    ),

                                    ((double.tryParse(widget.paymentData?[
                                                                'billAmount']
                                                            ?.toString() ??
                                                        "0") ??
                                                    0.0) -
                                                ((widget.paymentData?[
                                                                'paymentDetails']
                                                            ?['amount'] ??
                                                        0) /
                                                    100)) ==
                                            0.0
                                        ? Container()
                                        : Row(
                                            children: [
                                              Expanded(
                                                  child: Container(
                                                alignment: Alignment.topLeft,
                                                child: Row(
                                                  children: <Widget>[
                                                    Expanded(
                                                        child: Container(
                                                      // color: Colors.amber,
                                                      alignment:
                                                          Alignment.centerLeft,
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: <Widget>[
                                                          Text(
                                                            "Discount Amount",
                                                            style: TextStyle(
                                                              fontFamily:
                                                                  'Gilroy-SemiBold',
                                                              fontSize:
                                                                  FSTextStyle
                                                                      .h6size,
                                                              color:
                                                                  FsColor.black,
                                                            ),
                                                            textAlign:
                                                                TextAlign.left,
                                                          ),
                                                        ],
                                                      ),
                                                    )),
                                                  ],
                                                ),
                                              )),
                                              Expanded(
                                                child: Container(
                                                  alignment:
                                                      Alignment.centerRight,
                                                  child: Text(
                                                    '₹ ${((double.tryParse(widget.paymentData?['billAmount']?.toString() ?? "0") ?? 0.0) - ((widget.paymentData?['paymentDetails']?['amount'] ?? 0) / 100)).toString()}',
                                                    style: TextStyle(
                                                        fontFamily:
                                                            'Gilroy-SemiBold',
                                                        fontSize: 16,
                                                        color: FsColor.black),
                                                    textAlign: TextAlign.right,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),

                                    ((double.tryParse(widget.paymentData?[
                                                                'billAmount']
                                                            ?.toString() ??
                                                        "0") ??
                                                    0.0) -
                                                ((widget.paymentData?[
                                                                'paymentDetails']
                                                            ?['amount'] ??
                                                        0) /
                                                    100)) ==
                                            0.0
                                        ? Container()
                                        : SizedBox(
                                            height: 10.0,
                                            child: Divider(
                                                color: Theme.of(context)
                                                    .dividerColor,
                                                height: 2.0),
                                          ),
                                    // SizedBox(height: 5),
                                    Row(
                                      children: [
                                        Expanded(
                                            child: Container(
                                          alignment: Alignment.topLeft,
                                          child: Row(
                                            children: <Widget>[
                                              Expanded(
                                                  child: Container(
                                                // color: Colors.amber,
                                                alignment: Alignment.centerLeft,
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: <Widget>[
                                                    Text(
                                                      method,
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .titleSmall,
                                                      textAlign: TextAlign.left,
                                                    ),
                                                    SizedBox(height: 3),
                                                    (value == null ||
                                                            value == "")
                                                        ? Container()
                                                        : Text(
                                                            '$value',
                                                            style: Theme.of(
                                                                    context)
                                                                .textTheme
                                                                .bodyMedium,
                                                            textAlign:
                                                                TextAlign.left,
                                                          ),
                                                    (value2 == null ||
                                                            value2 == "")
                                                        ? Container()
                                                        : Text(
                                                            '$value2',
                                                            style: Theme.of(
                                                                    context)
                                                                .textTheme
                                                                .bodyMedium,
                                                            textAlign:
                                                                TextAlign.left,
                                                          ),
                                                  ],
                                                ),
                                              )),
                                            ],
                                          ),
                                        )),
                                        Expanded(
                                          child: Container(
                                            alignment: Alignment.centerRight,
                                            child: Text(
                                              '₹ ${((widget.paymentData?['paymentDetails']?['amount'] ?? 0) / 100).toString()}',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleMedium,
                                              textAlign: TextAlign.right,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ]),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(
                        height: 10.0,
                        child: Divider(
                            color: Theme.of(context).dividerColor, height: 2.0),
                      ),
                      Container(
                        padding: EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    GestureDetector(
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                                builder: (context) =>
                                                    HelpSupport()),
                                          );
                                        },
                                        child: Row(
                                          children: [
                                            Container(
                                              alignment: Alignment.topLeft,
                                              child: Row(
                                                children: <Widget>[
                                                  Icon(
                                                      FlutterIcon
                                                          .question_circle_o,
                                                      color: FsColor.darkgrey,
                                                      size: FSTextStyle.h3size),
                                                  SizedBox(width: 20),
                                                  Container(
                                                    alignment:
                                                        Alignment.centerLeft,
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: <Widget>[
                                                        Text(
                                                          FsString
                                                              .CONTACT_ONEAPP_SUPPORT,
                                                          style:
                                                              Theme.of(context)
                                                                  .textTheme
                                                                  .titleSmall,
                                                          textAlign:
                                                              TextAlign.left,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        )),
                                  ]),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 20.0),
                        alignment: Alignment.center,
                        child: TextButton(
                          child: Text(
                            'BACK TO HOME',
                            style: Theme.of(context)
                                .textTheme
                                .titleSmall!
                                .merge(TextStyle(color: kPrimaryRedColor)),
                            textAlign: TextAlign.right,
                          ),
                          onPressed: () {
                            backClick(context);
                          },
                        ),
                      ),
                    ]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  var value, method, value2;
  formatString(str) {
    return '********' + str.toString();
  }

  void backClick(context) {
    Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => MainDashboardV2()),
        (Route<dynamic> route) => true);
    // Navigator.of(context).pop(true);
  }

  formatTransactionId(val) {
    if (val == null || val.toString().isEmpty) {
      return "N/A";
    }

    String valStr = val.toString();
    if (valStr.length <= 4) {
      // If string is too short, just mask it completely
      return "****";
    }

    try {
      String maskedVal =
          valStr.replaceAll(valStr.substring(0, valStr.length - 4), "******");
      if (kDebugMode) {
        print(maskedVal);
      }
      return maskedVal;
    } catch (e) {
      if (kDebugMode) {
        print("Error formatting transaction ID: $e");
      }
      return "****";
    }
  }

  paymentDetails() {
    if (widget.paymentData!['paymentDetails'] == null) {
      method = "";
      value = "";
    } else if (widget.paymentData!['paymentDetails']["method"] == "upi") {
      method = widget.paymentData!['paymentDetails']["vpa"];
      value = (widget.paymentData!['paymentDetails']['acquirer_data']['rrn'])
          .toString();
      if (widget.paymentData!['paymentDetails']['acquirer_data']
              ['upi_transaction_id'] !=
          null) {
        value2 = "UPI Transaction ID:" +
            (formatTransactionId(widget.paymentData!['paymentDetails']
                    ['acquirer_data']['upi_transaction_id']))
                .toString();
      }
      if (value != null &&
          (value.toString() != "" || value.toString() != "null")) {
        value = "UTR:" + formatTransactionId(value).toString();
      }
    } else if (widget.paymentData!['paymentDetails']["method"] == "card") {
      method = widget.paymentData!['paymentDetails']["card"] == null
          ? ""
          : (widget.paymentData!['paymentDetails']["card"]['network']);
      value = widget.paymentData!['paymentDetails']["card"] == null
          ? ""
          : formatString(
              widget.paymentData!['paymentDetails']["card"]['last4']);
      if (value != null &&
          (value.toString() != "" || value.toString() != "null")) {
        value = "Card:" + value.toString();
      }
    } else if (widget.paymentData!['paymentDetails']["method"] ==
        "netbanking") {
      method = widget.paymentData!['paymentDetails']["bank"];
      value = (widget.paymentData!['paymentDetails']['acquirer_data']
          ['bank_transaction_id']);
      if (value != null &&
          (value.toString() != "" || value.toString() != "null")) {
        value = "UTR:" + value.toString();
      }
    } else if (widget.paymentData!['paymentDetails']["method"] == "wallet") {
      method = widget.paymentData!['paymentDetails']["wallet"];
      value = (widget.paymentData!['paymentDetails']['acquirer_data']['rrn']);
    } else {
      method = "";
      value = "";
    }

    setState(() {});
  }

  @override
  void setState(fn) {
    // TODO: implement setState
    super.setState(fn);
  }
}
