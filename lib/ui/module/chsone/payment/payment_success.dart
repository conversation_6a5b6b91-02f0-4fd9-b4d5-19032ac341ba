import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:sso_futurescape/custom_widgets/custom_elevated_btn.dart';

import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';
import 'package:sso_futurescape/ui/module/dashboard/new/helper/dashboard_helper.dart';
import 'package:sso_futurescape/utils/payment/transaction_status_handler.dart';

class PaymentSuccessPage extends StatefulWidget {
  final Map<String, dynamic>? transactionData;

  const PaymentSuccessPage({Key? key, this.transactionData}) : super(key: key);

  @override
  _PaymentSuccessPageState createState() => new _PaymentSuccessPageState();
}

class _PaymentSuccessPageState extends State<PaymentSuccessPage> {
  TextEditingController userNameController = new TextEditingController();

  @override
  void dispose() {
    super.dispose();
  }

  String _getPageTitle() {
    if (widget.transactionData == null) return "Payment Successful";

    TransactionStatus status =
        TransactionStatusHandler.detectStatus(widget.transactionData);
    return TransactionStatusHandler.getStatusTitle(status);
  }

  Color _getAppBarColor() {
    if (widget.transactionData == null) return Color(0xff64e291);

    TransactionStatus status =
        TransactionStatusHandler.detectStatus(widget.transactionData);
    return TransactionStatusHandler.getStatusColor(status);
  }

  String _getLottieUrl() {
    if (widget.transactionData == null) {
      return 'https://fstech-cms-db.s3.ap-south-1.amazonaws.com/payment_successfull_a5069f5dd5.json';
    }

    TransactionStatus status =
        TransactionStatusHandler.detectStatus(widget.transactionData);
    return TransactionStatusHandler.getLottieUrl(status);
  }

  String _getStatusMessage() {
    if (widget.transactionData == null) {
      return 'We received your payment information. The amount will reflect in your balance once processed.';
    }

    TransactionStatus status =
        TransactionStatusHandler.detectStatus(widget.transactionData);
    String defaultMessage = TransactionStatusHandler.getStatusMessage(status);

    // Use custom message if available, otherwise use default
    return widget.transactionData!["message"]?.toString() ?? defaultMessage;
  }

  Widget _buildTransactionDetailsCard() {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 20),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Transaction Details",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            SizedBox(height: 12),
            _buildDetailRow("Transaction ID",
                widget.transactionData!["transaction_id"]?.toString() ?? "N/A"),
            _buildDetailRow("Amount",
                "₹${widget.transactionData!["amount"]?.toString() ?? "N/A"}"),
            _buildDetailRow("Status",
                widget.transactionData!["status"]?.toString() ?? "N/A"),
            _buildDetailRow(
                "Date & Time",
                _formatDateTime(
                    widget.transactionData!["timestamp"]?.toString())),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          Flexible(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(String? timestamp) {
    if (timestamp == null) return "N/A";
    try {
      DateTime dateTime = DateTime.parse(timestamp);
      return "${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}";
    } catch (e) {
      return timestamp;
    }
  }

  @override
  Widget build(BuildContext context) {
    return MyScrollView(
      hasBackButton: false,
      bottomNavigationBar: SecuredByBBPS(),
      // floatingActionButton: CustomLargeBtn(
      //
      // )
      pageTitle: _getPageTitle(),
      appBarColor: _getAppBarColor(),
      pageBody: Container(
        child: Stack(
          children: <Widget>[
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Container(
                    padding: EdgeInsets.fromLTRB(20.0, 35.0, 20.0, 0.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      children: <Widget>[
                        Column(
                          children: <Widget>[
                            Container(
                              child: Lottie.network(_getLottieUrl(),
                                  height: 220.0,
                                  fit: BoxFit.contain,
                                  repeat: false),
                            ),
                            SizedBox(height: 15),
                            // Transaction Details Card
                            if (widget.transactionData != null) ...[
                              _buildTransactionDetailsCard(),
                              SizedBox(height: 15),
                            ],
                            Container(
                              alignment: Alignment.topCenter,
                              child: Text(
                                _getStatusMessage(),
                                textAlign: TextAlign.center,
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                            ),
                            SizedBox(height: 15),
                            CustomLargeBtn(
                              text: "Go to Dashboard",
                              onPressed: () {
                                DashboardHelper.getInstance()
                                    .openDashboard(context);
                              },
                            )

                            // Container(
                            //   child: Text(
                            //     'The amount would reflect in your due, subject to realisation.',
                            //     textAlign: TextAlign.center,
                            //     style: Theme.of(context).textTheme.bodyLarge,
                            //   ),
                            // ),
                          ],
                        ),
                      ],
                    )),
              ],
            ),
            // Positioned(
            //   bottom: 0.0,
            //   left: 0.0,
            //   right: 0.0,
            //   child: Row(
            //     crossAxisAlignment: CrossAxisAlignment.center,
            //     mainAxisAlignment: MainAxisAlignment.center,
            //     children: <Widget>[
            //       Container(
            //         margin: EdgeInsets.only(bottom: 20.0, left: 20.0),
            //         child: CustomLargeBtn(
            //           text: "Explore More",
            //           onPressed: (){
            //             DashboardHelper.getInstance().openDashboard(context);
            //           },
            //         )
            //
            //
            //
            //         // ElevatedButton(
            //         //   onPressed: () {
            //         //     /*Navigator.pushAndRemoveUntil(
            //         //             context,
            //         //             MaterialPageRoute(
            //         //                 builder: (context) => MainDashboard()),
            //         //                 (Route<dynamic> route) => false);*/
            //         //
            //         //
            //         //   },
            //         //   style: ElevatedButton.styleFrom(
            //         //     padding: EdgeInsets.fromLTRB(30.0, 10.0, 30.0, 10.0), backgroundColor: FsColor.primaryflat,
            //         //     shape: RoundedRectangleBorder(
            //         //       borderRadius: BorderRadius.circular(4.0),
            //         //     ),
            //         //     textStyle: TextStyle(
            //         //       fontSize: FSTextStyle.h6size,
            //         //       fontFamily: 'Gilroy-SemiBold',
            //         //     ),
            //         //   ),
            //         //   child: Text('Explore More'),
            //         // ),
            //       )
            //     ],
            //   ),
            // ),
          ],
        ),
      ),
    );

//     Scaffold(
//       appBar: new AppBar(

//         backgroundColor: Colors.green,
//         elevation: 0.0,
//         title: new Text(
//           'Payment Successfull',
//           style: FSTextStyle.appbartextlight,
//         ),
//         leading: Container() /*FsBackButtonlight()*/,
//       ),
//       body: Container(
//         child: Stack(
//           children: <Widget>[
//             Column(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               mainAxisSize: MainAxisSize.max,
//               crossAxisAlignment: CrossAxisAlignment.center,
//               children: <Widget>[
//                 Container(
//                     padding: EdgeInsets.fromLTRB(20.0, 35.0, 20.0, 0.0),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.center,
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       mainAxisSize: MainAxisSize.max,
//                       children: <Widget>[
//                         Column(
//                           children: <Widget>[
//                             Container(
//                               child: new Image.asset(
//                                 'assets/images/img.png',
//                                 height: 220.0,
//                                 fit: BoxFit.contain,
//                               ),
//                             ),
//                             SizedBox(height: 15),
//                             Container(
//                               alignment: Alignment.topCenter,
//                               child: Text(
//                                 'We received your payment information.'
//                                     .toLowerCase(),
//                                 textAlign: TextAlign.center,
//                                 style: TextStyle(
//                                     fontFamily: 'Gilroy-Regular',
//                                     letterSpacing: 1.0,
//                                     fontSize: FSTextStyle.h3size,
//                                     height: 1.5,
//                                     color: FsColor.darkgrey),
//                               ),
//                             ),
//                             SizedBox(height: 15),
//                             Container(
//                               child: Text(
//                                 'The amount would reflect in your due, subject to realisation.'
//                                     .toLowerCase(),
//                                 textAlign: TextAlign.center,
//                                 style: TextStyle(
//                                     fontFamily: 'Gilroy-Regular',
//                                     letterSpacing: 1.0,
//                                     fontSize: FSTextStyle.h6size,
//                                     height: 1.5,
//                                     color: FsColor.darkgrey),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ],
//                     )),
//               ],
//             ),
//             Positioned(
//               bottom: 0.0,
//               left: 0.0,
//               right: 0.0,
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: <Widget>[
//                   Container(
//                     margin: EdgeInsets.only(bottom: 20.0, left: 20.0),
//                     child: GestureDetector(
//                       child:ElevatedButton(
//   onPressed: () {
//     /*Navigator.pushAndRemoveUntil(
//         context,
//         MaterialPageRoute(
//             builder: (context) => MainDashboard()),
//             (Route<dynamic> route) => false);*/

//     DashboardHelper.getInstance().openDashboard(context);
//   },
//   style: ElevatedButton.styleFrom(
//     padding: EdgeInsets.fromLTRB(30.0, 10.0, 30.0, 10.0), backgroundColor: FsColor.primaryflat,
//     shape: RoundedRectangleBorder(
//       borderRadius: BorderRadius.circular(4.0),
//     ),
//     textStyle: TextStyle(
//       fontSize: FSTextStyle.h6size,
//       fontFamily: 'Gilroy-SemiBold',
//     ),
//   ),
//   child: Text('Explore More'),
// )

//                     ),
//                   )
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
  }
}
