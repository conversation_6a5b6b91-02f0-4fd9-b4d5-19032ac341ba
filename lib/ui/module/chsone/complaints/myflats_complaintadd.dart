import 'dart:io';

import 'package:common_config/utils/toast/toast.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sso_futurescape/config/colors/color.dart';
import 'package:sso_futurescape/config/strings/strings.dart';
import 'package:sso_futurescape/custom_widgets/custom_elevated_btn.dart';
import 'package:sso_futurescape/custom_widgets/custom_form_widget.dart';
import 'package:sso_futurescape/custom_widgets/my_scroll_widget.dart';
import 'package:sso_futurescape/presentor/module/chsone/complaints/complaint_presenter.dart';
import 'package:sso_futurescape/presentor/module/chsone/complaints/complaint_view.dart';
import 'package:sso_futurescape/ui/base/loading_constants.dart';
import 'package:sso_futurescape/ui/module/chsone/complaints/myflats_complaintslist.dart';
import 'package:sso_futurescape/ui/module/chsone/utils/loader_utils.dart';
import 'package:sso_futurescape/ui/module/sso/profile/utils/mutiple_image_video_picker_handler.dart';
import 'package:sso_futurescape/ui/widgets/my_flutter_app_icons.dart';
import 'package:sso_futurescape/user_consent_helper/permission_helper.dart';
import 'package:sso_futurescape/utils/app_constant.dart';
import 'package:sso_futurescape/utils/app_utils.dart';
import 'package:sso_futurescape/utils/ui/loading_error_utils.dart';
import 'package:sso_futurescape/utils/widget_utils.dart';

// ignore: must_be_immutable
class MyFlatsComplaintsAdd extends StatefulWidget {
  var currentUnitDetails;

  MyFlatsComplaintsAdd(this.currentUnitDetails);

  @override
  _MyFlatsComplaintsAddState createState() =>
      new _MyFlatsComplaintsAddState(currentUnitDetails);
}

class _MyFlatsComplaintsAddState extends State<MyFlatsComplaintsAdd>
    implements ComplaintView, AddComplaintView, MultipleImgVideoPickerListener {
  late ComplaintPresenter presenter;
  bool isLoading = true;

  bool _loadError = false;
  LoadingErrorType? _loadErrorType;
  String? _loadErrorMsg;

  TextEditingController _complaintDetailsController =
      new TextEditingController();
  TextEditingController _complaintSubjectController =
      new TextEditingController();

  String? _complaintDetailsErrorText;
  String? _complaintSubjectErrorText;
  var currentUnitDetails;
  var currentUnit;
  var socId;

  _MyFlatsComplaintsAddState(currentUnitDetails) {
    this.currentUnitDetails = currentUnitDetails;
    this.currentUnit = currentUnitDetails['unit_id'];
    this.socId = currentUnitDetails['soc_id'];
  }

  File? fileUploaded = null;
  List<File> filesUploaded = [];

  List uploadimage = [
    {
      "name": "Camera",
      "img":
          "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/camera_87b3583d87.json",
    },
    {
      "name": "Gallery",
      "img":
          "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/gallery_c4b4bfb613.json",
    },
    // {n
    //   "name": "Drive",
    //   "img": "images/drive.png",
    // }
  ];
  AnimationController? _controller;
  MultipleImgVideoPickerHandler? multipleImgVideoPickerHandler;

  //_MyFlatsComplaintsAddState(this.currentUnit);

  @override
  void initState() {
    _loadRaiseComplaintPage();
    super.initState();
  }

  List<dynamic>? _topic; // Option 1
  // HashMap<int,String> _topic;
  var _selectedtopic;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);

          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: MyScrollView(
          pageTitle: 'Raise Complaint',
          // resizeToAvoidBottomInset: false,
//           appBar: new AppBar(
//             elevation: Theme.of(context).appBarTheme.elevation,
//             titleTextStyle: Theme.of(context).appBarTheme.titleTextStyle,
//             backgroundColor: Theme.of(context).colorScheme.surface,
//             title: new Text(
//               'Raise Complaint'.toLowerCase(),
//               // style: FSTextStyle.appbartextlight,
//             ),
//             leading: BackButton1(
// //          backEvent: (context) {
// //            backHandler(context);
// //          },
//                 ),
//             actions: <Widget>[],
//           ),
          pageBody: _loadError
              ? _buildErrorWidget()
              : isLoading
                  ? _getLoaderWidget()
                  : Column(
                      children: <Widget>[
                        Container(
                          alignment: Alignment.topLeft,
                          padding: EdgeInsets.symmetric(
                            vertical: 20,
                          ),
                          child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Expanded(
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface,
                                        // width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(15.0),
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton(
                                        isExpanded: true,
                                        hint: Text(
                                          'Choose a Topic',
                                          style: TextStyle(
                                            fontSize: 18,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurface
                                                .withOpacity(0.5),
                                          ),
                                        ),
                                        value: _selectedtopic == null
                                            ? null
                                            : _selectedtopic,
                                        onChanged: (dynamic newValue) {
                                          setState(() {
                                            _selectedtopic = newValue;
                                            if (kDebugMode) {
                                              print(_selectedtopic);
                                            }
                                          });
                                        },
                                        items:
                                            _topic != null && _topic!.length > 0
                                                ? _topic!.map((topic) {
                                                    return DropdownMenuItem(
                                                      child: Text(
                                                        topic["help_topic"],

                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .titleMedium!
                                                            .copyWith(
                                                                color: Theme.of(
                                                                        context)
                                                                    .colorScheme
                                                                    .onSurface),

                                                        // style: TextStyle(
                                                        //   fontSize:
                                                        //       FSTextStyle.h6size,
                                                        //   color: Color(0xFF545454),
                                                        //   fontFamily:
                                                        //       'Gilroy-SemiBold',
                                                        // ),
                                                      ),
                                                      value: topic,
                                                    );
                                                  }).toList()
                                                : null,
                                      ),
                                    ),
                                  ),
                                ),
                              ]),
                        ),
                        CustomTextField(
                          textController: _complaintSubjectController,
                          errorText: _complaintSubjectErrorText,
                          hintText: 'Enter Complaint Subject',
                          title: 'Subject',
                        ),
                        CustomTextField(
                          textController: _complaintDetailsController,
                          errorText: _complaintDetailsErrorText,
                          lines: 3,
                          hintText: 'Enter Complaint Details',
                          title: 'Details',
                        ),
                        fileUploaded == null
                            ? Container(
                                margin: EdgeInsets.all(20),
                                child: Column(
                                  children: <Widget>[
                                    GestureDetector(
                                      child: Container(
                                        margin: EdgeInsets.only(bottom: 20),
                                        width: 100,
                                        height: 100,
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Icon(
                                          Symbols.upload,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurface,
                                          size: 40,
                                        ),
                                      ),
                                      onTap: () {
                                        _uploadImageDialog();
                                      },
                                    ),
                                    Text(
                                      'Upload image here',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium,
                                    )
                                  ],
                                ),
                              )
                            : Container(
                                margin: EdgeInsets.fromLTRB(0, 20, 0, 20),
                                child: ListTile(
                                  contentPadding: EdgeInsets.zero,
                                  leading: Icon(
                                    Symbols.photo,
                                    color:
                                        Theme.of(context).colorScheme.onSurface,
                                  ),
                                  title: Text(
                                    fileUploaded!.path,
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  //subtitle: Text(fileUploaded.path),
                                  trailing: IconButton(
                                    icon: Icon(
                                      FlutterIcon.cancel_1,
                                      color: Colors.red,
                                    ),
                                    onPressed: () {
                                      fileUploaded = null;
                                      setState(() {});
                                    },
                                  ),
                                ),
                              ),
                        CustomLargeBtn(
                            onPressed: () {
                              _loadRaiseComplaintPage(tag: 'Raise Complaint');
                            },
                            text: "Raise Complaint")

                        // Container(
                        //   alignment: Alignment.center,
                        //   child: GestureDetector(
                        //     child: ElevatedButton(
                        //       style: ElevatedButton.styleFrom(
                        //           backgroundColor:
                        //               Theme.of(context).colorScheme.secondary,
                        //           textStyle:
                        //               Theme.of(context).textTheme.labelLarge,
                        //           padding: EdgeInsets.fromLTRB(
                        //               30.0, 10.0, 30.0, 10.0),
                        //           shape: new RoundedRectangleBorder(
                        //             borderRadius:
                        //                 new BorderRadius.circular(4.0),
                        //           )),
                        //       child: Text('Raise Complaint'),
                        //       onPressed: () {
                        //         _loadRaiseComplaintPage(tag: 'Raise Complaint');
                        //       },
                        //     ),
                        //   ),
                        // ),
                      ],
                    ),
        ));
  }

  void _loadRaiseComplaintPage({tag}) {
    AppUtils.checkInternetConnection().then((value) {
      if (value) {
        _loadError = false;
        _loadErrorType = null;
        _loadErrorMsg = null;
        if (tag == 'Raise Complaint') {
          setState(() {
            _complaintSubjectErrorText =
                _complaintSubjectController.text.length > 2
                    ? null
                    : 'Subject must be greater than 2 characters';
            _complaintDetailsErrorText =
                _complaintDetailsController.text.length > 2
                    ? null
                    : 'Details must be greater than 2 characters';
          });
          if (_complaintSubjectErrorText == null &&
              _complaintDetailsErrorText == null) {
            requestApiCall();
          }
        } else {
          ///////////////////initstate////////////
          presenter = new ComplaintPresenter(this);
          getHelpTopics();
          ///////////////////////////////////////
        }
      } else {
        _loadError = true;
        _loadErrorMsg = FsString.ERROR_NO_INTERNET_RETRY;
        _loadErrorType = LoadingErrorType.INTERNET;
      }
      setState(() {});
    });
  }

  Widget _buildErrorWidget() {
    return Center(
      child: WidgetUtils.getErrorWidget(
          module: AppConstant.ONE_PAY,
          errorMsg: _loadErrorMsg!,
          errorType: _loadErrorType,
          showErrorIcon: true,
          shouldRetry: LoadingErrorUtils.canRetry(_loadErrorType),
          onRetryPressed: () {
            _loadRaiseComplaintPage();
          },
          retryButtonColor: FsColor.primaryflat),
    );
  }

  Widget _getLoaderWidget() {
    return LoaderUtils.getLoaderWidget(context, Screen.POST_COMPLAINT);
  }

  void _uploadImageDialog() {
    // flutter defined function

    // showDialog(
    //   context: context,
    //   builder: (BuildContext context) {
    //     // return object of type Dialog
    //     return AlertDialog(
    //       title: new Text("choose one of the below options",
    //           textAlign: TextAlign.center,
    //           style: Theme.of(context).textTheme.titleLarge),
    //       shape: new RoundedRectangleBorder(
    //         borderRadius: new BorderRadius.circular(7.0),
    //       ),
    //       content: Container(
    //         height: 120.0,
    //         alignment: Alignment.center,
    //         // width: 900.0,
    //         child: Column(
    //           mainAxisAlignment: MainAxisAlignment.center,
    //           crossAxisAlignment: CrossAxisAlignment.center,
    //           mainAxisSize: MainAxisSize.max,
    //           children: <Widget>[
    //             Container(
    //               padding: EdgeInsets.only(top: 10, left: 0),
    //               height: 120,
    //               width: MediaQuery.of(context).size.width,
    //               child: ListView.builder(
    //                 scrollDirection: Axis.horizontal,
    //                 shrinkWrap: true,
    //                 primary: false,
    //                 // ignore: unnecessary_null_comparison
    //                 itemCount: uploadimage == null ? 0 : uploadimage.length,
    //                 itemBuilder: (BuildContext context, int index) {
    //                   Map upload = uploadimage[index];
    //                   return Padding(
    //                     padding: const EdgeInsets.only(right: 0, top: 10),
    //                     child: InkWell(
    //                       child: Container(
    //                         height: 120,
    //                         width: 90,
    //                         child: Column(
    //                           mainAxisAlignment: MainAxisAlignment.center,
    //                           children: <Widget>[
    //                             SizedBox(height: 8),
    //                             ClipRRect(
    //                               // borderRadius: BorderRadius.circular(50),
    //                               child: Image.asset(
    //                                 "${upload["img"]}",
    //                                 height: 50,
    //                                 width: 50,
    //                                 fit: BoxFit.cover,
    //                               ),
    //                             ),
    //                             SizedBox(height: 8),
    //                             Container(
    //                               alignment: Alignment.center,
    //                               child: Text(
    //                                 "${upload["name"]}",
    //                                 style:
    //                                     Theme.of(context).textTheme.bodyMedium,
    //                                 maxLines: 2,
    //                                 textAlign: TextAlign.left,
    //                               ),
    //                             ),
    //                           ],
    //                         ),
    //                       ),
    //                       onTap: () {
    //                         //print("onTap" + upload["name"]);
    //                         uploadImageEvent(upload["name"]);
    //                         Navigator.pop(context);
    //                       },
    //                     ),
    //                   );
    //                 },
    //               ),
    //             ),
    //           ],
    //         ),
    //       ),
    //       actions: <Widget>[
    //         // usually buttons at the bottom of the dialog
    //         TextButton(
    //           child: new Text(
    //             "Cancel",
    //             style: Theme.of(context)
    //                 .textTheme
    //                 .titleSmall!
    //                 .merge(TextStyle(color: Color(0xFF545454))),
    //           ),
    //           onPressed: () {
    //             Navigator.of(context, rootNavigator: true).pop('dialog');
    //           },
    //         ),
    //       ],
    //     );
    showModalBottomSheet(
      backgroundColor: Theme.of(context).colorScheme.surface,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(16.0),
          height: MediaQuery.of(context).size.height * 0.4,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text(
                    "Choose Below",
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                  IconButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      icon: Icon(
                        Symbols.cancel,
                        color: Theme.of(context).colorScheme.onSurface,
                      ))
                ],
              ),
              SizedBox(
                height: 10,
              ),
              Container(
                width: MediaQuery.of(context).size.width,
                height: 150.0,
                alignment: Alignment.center,
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  scrollDirection: Axis.horizontal,
                  shrinkWrap: true,
                  primary: false,
                  itemCount: uploadimage == null ? 0 : uploadimage.length,
                  itemBuilder: (BuildContext context, int index) {
                    Map upload = uploadimage[index];
                    return InkWell(
                      child: Container(
                        margin: EdgeInsets.only(right: 40),
                        height: 120,
                        width: 90,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            SizedBox(height: 8),
                            ClipRRect(
                              child: Lottie.network(
                                "${upload["img"]}",
                                height: 100,
                                width: 100,
                                fit: BoxFit.cover,
                              ),
                            ),
                            SizedBox(height: 8),
                            Container(
                              alignment: Alignment.center,
                              child: Text(
                                "${upload["name"]}",
                                style: Theme.of(context).textTheme.bodyMedium,
                                maxLines: 2,
                                textAlign: TextAlign.left,
                              ),
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        uploadImageEvent(upload["name"]);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
              // Padding(
              //   padding: const EdgeInsets.all(8.0),
              //   child: TextButton(
              //     child: Text(
              //       "Cancel",
              //       style: Theme.of(context).textTheme.titleSmall!.merge(TextStyle(color: Color(0xFF545454))),
              //     ),
              //     onPressed: () {
              //       Navigator.of(context).pop();
              //     },
              //   ),
              // ),
            ],
          ),
        );
      },
    );
  }

  void uploadImageEvent(String? upload_image_type) {
    if (upload_image_type == "Gallery") {
      openGallery();
    } else if (upload_image_type == "Camera") {
      openCamera();
    }
  }

  // openCamera() {
  //   PermissionHelper.instance
  //       .requestCameraPermissionForOneSociety(context)
  //       .then((value) {
  //     if (value) {
  //       multipleImgVideoPickerHandler = null;
  //       multipleImgVideoPickerHandler =
  //           new MultipleImgVideoPickerHandler(this, _controller, limit: 1);
  //       multipleImgVideoPickerHandler!.openCameraForImage(context);
  //     }
  //   });
  // }

  void openCamera() {
    PermissionHelper.instance
        .requestCameraPermissionForOneSociety(context)
        .then((isPermissionGranted) {
      if (isPermissionGranted) {
        multipleImgVideoPickerHandler = MultipleImgVideoPickerHandler(
          this,
          _controller,
          limit: 1,
        );

        multipleImgVideoPickerHandler!.openCameraForImage(context);
      } else {
        // Permission denied
        print("Camera permission denied!");
      }
    }).catchError((error) {
      print("An error occurred while requesting camera permission: $error");
    });
  }

  void _showPermissionDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Permission Required'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings(); // Navigate to app settings
            },
            child: Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  void openGallery() async {
    try {
      bool isPermissionGranted = await PermissionHelper.instance
          .requestStoragePermissionForOnesociety(context);

      if (isPermissionGranted) {
        multipleImgVideoPickerHandler = MultipleImgVideoPickerHandler(
          this,
          _controller,
          limit: 1,
        );
        multipleImgVideoPickerHandler!.openGalleryForImage2(context);
      } else {
        print("Gallery opening denied due to permission issues!");
      }
    } catch (e) {
      print("Error while requesting permissions or opening gallery: $e");
    }
  }

  //
  // openGallery() {
  //   PermissionHelper.instance
  //       .requestStoragePermissionForOnesociety(context)
  //       .then((value) {
  //     if (value) {
  //       multipleImgVideoPickerHandler = null;
  //       multipleImgVideoPickerHandler =
  //           new MultipleImgVideoPickerHandler(this, _controller, limit: 1);
  //       //}
  //       multipleImgVideoPickerHandler!.openGalleryForImage2(context);
  //     }
  //   });
  // }

  @override
  userImages(List<File> _images, String errormsg) {
    if (errormsg != "") {
      Toastly.error(context, errormsg, gravity: Gravity.BOTTOM);
    }

    // ignore: unnecessary_null_comparison
    if (_images != null && _images.length != 0) {
      //print(_images);
      setState(() {
        //isImageDialog = false;
        this.fileUploaded = _images[0];
      });
      //uploadImageApiCall();
    }
  }

  Future getFile() async {
    PermissionHelper.instance
        .requestStoragePermissionForOnesociety(context)
        .then((value) async {
      if (value) {
        FilePickerResult? result = await FilePicker.platform.pickFiles();
        if (result != null) {
          fileUploaded = File(result.files.single.path!);
        }
        if (fileUploaded != null) {}
        setState(() {});
      }
    });
  }

  void getHelpTopics() {
    isLoading = true;
    //print("=====================================>>");
    //print(currentUnit);
    presenter.getHelpdeskComplaintTopic(socId);
  }

  @override
  onErrorHelpTopics(errror) {
    Toastly.error(context, "Issue has not been raised");
  }

  @override
  onFailureHelpTopics(failed) {
    isLoading = false;

    //print(failed);
    Toastly.error(context, AppUtils.errorDecoder(failed));
    setState(() {});
  }

  @override
  onSuccessHelpTopics(response) {
    setState(() {
      isLoading = false;
      this._topic = response['data'];
    });
  }

  @override
  onSuccessAddComplaint(response) {
    isLoading = false;
    Toastly.success(context, response['message']);
    _finish(context);
  }

  void requestApiCall() {
    //print("dddddd");
    String detail = _complaintDetailsController.text.trim();
    String subject = _complaintSubjectController.text.trim();
    String? topic;
    bool isValid = true;
    var currentUnit = this.currentUnit;
    //print("dddddd");
    if (_selectedtopic != null) {
      topic = _selectedtopic["help_topic_id"].toString();
    } else {
      topic = null;
      isValid = false;
    }
    _complaintSubjectErrorText = null;
    _complaintDetailsErrorText = null;

    if (subject.trim().isEmpty) {
      isValid = false;
      _complaintSubjectErrorText = "please enter subject";
    }
    if (detail.trim().isEmpty) {
      isValid = false;
      _complaintDetailsErrorText = "please enter deatails";
    }

    if (topic == null) {
      Toastly.error(context, "please select topic");
      isValid = false;
    }

    // var currentUnit="12";
    if (isValid) {
      //print("asaasasasas");
      isLoading = true;
      presenter.addComplait(detail, subject, currentUnit, topic!,
          file: fileUploaded);
    }
    setState(() {});
  }

  @override
  void onError(String? response) {
    isLoading = false;
    Toastly.error(context, AppUtils.errorDecoder(response));
    setState(() {});
  }

  @override
  void onFailure(String? response) {
    isLoading = false;
    Toastly.error(context, AppUtils.errorDecoder(response));
    setState(() {});
  }

  void _finish(BuildContext context) {
    int pops = 0;
    int totalPops = 2;
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
            builder: (context) => MyFlatsComplaintsList(currentUnitDetails)),
        (Route<dynamic> route) => ((++pops) > totalPops));
  }
}
