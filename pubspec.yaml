name: sso_futurescape
description: A new Flutter application 5.3.4 (167) Android and 4.3.1 (97) iOS.

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as vers
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.htmlionName while build-number used as versionCode.
version: 5.3.5+169

environment:
  sdk: '>=3.5.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  collection: ^1.18.0
  #  dropdownfield: ^1.0.3
  flutter_onboarding_slider: ^1.0.11
  dropdownfield2: ^1.0.7
  material_symbols_icons: ^4.2789.0
  percent_indicator: ^4.2.3
  salomon_bottom_bar: ^3.3.2
  m3_carousel: ^2.0.2
  flutter_carousel_widget: ^3.1.0
  share_plus: ^10.1.4
  upgrader: ^11.3.0
  flutter_spinkit: ^5.2.1
  auto_animated: ^3.2.0
  dash_chat_2: ^0.0.21
  app_links: ^6.3.2
  store_redirect: ^2.0.2
  in_app_update: ^4.2.3
  modal_bottom_sheet: ^3.0.0
  animations: ^2.0.11
  dart_amqp: ^0.3.1
  flutter_slidable: ^3.1.2
  ndialog: ^4.4.0
  add_2_calendar: ^3.0.1
  flutter_secure_storage: ^9.2.4
  easy_stepper: ^0.8.5+1
  dart_jsonwebtoken: ^2.15.0
  dio: ^4.0.6
  flutter_map: ^7.0.2
  #  geolocator: ^12.0.0
  #  geocoding: ^3.0.0
  #  dartz: ^0.10.1
  flutter_animate: ^4.5.2
  table_calendar: ^3.2.0
  # otp_autofill: ^3.0.2 # TEMPORARILY COMMENTED OUT
  pointycastle: ^3.9.1
#  beamer: ^1.7.0

dev_dependencies:


  razorpay_flutter: ^1.3.7

  # razorpay_flutter:
  #  git:
  #    url: git://github.com/razorpay/razorpay-flutter.git
  #    ref: master

  # netcomm:
  #   git:
  #     url: http://git.futurescapetech.com/ashish.sharma/netcomm.git
  #     ref: dev
  flutter_test:
    sdk: flutter
  cupertino_icons: ^1.0.8
  url_launcher: ^6.3.1
  flutter_map: ^7.0.2
  bubble: ^1.2.1
  flutter_bloc: ^8.1.6
  fluro: any
  flutter_calendar_carousel: ^2.4.4
  youtube_player_flutter: ^9.0.4
  flutter_tts: ^4.0.2
  datetime_picker_formfield: ^2.0.1
  flutter_datetime_picker: ^1.5.1
  date_picker_plus: ^4.1.0
  introduction_screen: ^3.1.14
  mime: ^1.0.6
  intl: ^0.19.0
  flutter_gradient_animation_text: ^1.0.2
  device_apps: ^2.2.0
  dropdown_button2: ^2.3.9
  random_avatar: ^0.0.8
  one_clock: ^2.0.1
  chips_choice: ^3.0.1
  page_flip: ^0.2.3
  open_settings_plus: ^0.3.3
  intl_phone_number_input: ^0.7.4
  #flutter_html: ^1.0.2
  #flutter_html: ^0.11.1
  #flutter_google_places: ^0.3.0
  #connectivity: ^3.0.6
  shared_preferences: ^2.3.2
  qr_flutter: ^4.1.0
  # git:
  #   url: git://github.com/lukef/qr.flutter.git
  encrypt: ^5.0.3
  common_config:
    path: './common_config'
  http: ^1.2.2
  image: ^4.3.0
  localstorage: ^4.0.1+4
  #firebase_analytics: ^5.0.16
  flutter_feather_icons: ^2.0.0+1

  # firebase_core: ^1.7.0 # previous stable version used was ^0.4.4+3
  firebase_core: ^2.32.0
  firebase_database: ^10.5.7
  firebase_messaging: ^14.9.4
  firebase_crashlytics: ^3.5.7
  # firebase_database: ^8.0.0 # previous stable version used was ^3.1.5
  # firebase_messaging: ^10.0.8
  #  firebase_crashlytics: ^2.2.3 # previous stable version used was ^0.1.3+3
  # firebase_dynamic_links: ^2.0.10 # upgrade needed todo
  #  firebase_analytics: ^10.8.10
  # firebase_remote_config: ^0.11.0+1
  firebase_dynamic_links: ^5.5.7
  firebase_remote_config: ^4.4.7
  firebase_core_platform_interface: ^5.3.0
  sentry_flutter: ^8.0.0
  camera: ^0.11.1
  # firebase_in_app_messaging: ^0.5.0+10
  firebase_in_app_messaging: ^0.7.5+7
  flutter_image_compress: ^2.3.0
  # firebase_core_platform_interface: 4.5.1
  phone_numbers_parser: ^9.0.1
  file_picker: ^8.0.7
  marquee: ^2.3.0
  flutter_libphonenumber: ^2.3.3
  #  libphonenumber: ^2.0.2
  settings_ui: ^2.0.2
  flutter_downloader: ^1.11.8
  dlibphonenumber: ^1.1.24
  socket_io_client: ^3.0.2

  carousel_slider: ^5.0.0
  #facebook_app_events: ^0.19.0
  uni_links: ^0.5.1
  dropdownfield2: ^1.0.7
  simple_gradient_text: ^1.3.0
  confetti: ^0.8.0
  pinput: ^5.0.0
  video_compress: ^3.1.3
  flutter_swipe_action_cell: ^3.1.5
  share: ^2.0.4
  path_provider: ^2.1.4
  package_info_plus: ^5.0.1
  device_info_plus: ^9.1.0
  #  image_picker:  ^1.1.1
  wakelock_plus: ^1.2.8
  #image: ^2.1.15
  video_player: ^2.9.2
  #bottom navigatin bar
  sliding_clipped_nav_bar: ^3.1.1
  location: ^7.0.0
  google_maps_flutter: ^2.9.0
  # geocoder: ^0.2.1
  #  flutter_webview_plugin: ^0.4.0
  #  webview_flutter: ^4.7.0
  flutter_local_notifications: ^17.2.3
  get: ^4.6.6
  motion_tab_bar_v2: ^0.3.0
  flutter_email_sender: ^6.0.3
  #  permission_handleer: ^11.3.1
  flutter_swiper_plus: ^2.0.4
  app_settings: ^5.1.1
  appsflyer_sdk: ^6.15.1
  photo_view: ^0.15.0
  # app_review: ^2.0.1
  # rate_my_app: ^0.7.2
  #in_app_update: ^1.1.11
  enum_to_string: ^2.0.1
  in_app_review: ^2.0.9
  scrollable_positioned_list: ^0.3.8
  #flutter_sms: ^2.3.3
  group_radio_button: ^1.3.0
  supabase_flutter: ^2.8.0
  #  input_calculator: 2.0.0
  local_auth: ^2.3.0
  # git:
  #   url: https://github.com/yanisalfian/flutter-phone-direct-caller.git
  #  call_number: ^0.0.1
  flutter_contacts: ^1.1.9
  random_color: ^1.0.6-nullsafety
  #flutter_facebook_app_links: ^3.0.2
  badges: ^3.1.2
  sqflite: ^2.4.0
  #firebase_admob: ^0.9.3+2
  google_fonts: ^6.2.1
  cached_network_image: ^3.4.0
  auto_size_text: ^3.0.0
  email_validator: ^3.0.0
  shimmer: ^3.0.0
  easy_localization: ^3.0.7
  social_share: ^2.3.1
  flutter_slidable: ^3.1.1
  flutter_staggered_grid_view: ^0.7.0
  #  facebook_auth: ^0.0.3
  #  flutter_login_facebook: ^1.9.0
  #  meta_facebook_login: ^4.0.3
  flutter_staggered_animations: ^1.1.1
  #page_transition: ^1.1.5
  #image: ^2.0.0
  #photo_view: ^0.9.2
  #app_review: ^2.0.1
  need_resume: ^1.0.7+1
  page_transition: ^2.1.0
  smooth_page_indicator: ^1.2.0+3
  concentric_transition: ^1.0.3
  mvc_pattern: ^8.12.0
  global_configuration: ^2.0.0
  html: ^0.15.4
  flutter_svg: ^2.0.10+1
  pin_code_fields: ^8.0.1
  intl_utils: ^2.8.7
  flutter_html: ^3.0.0-beta.2 #
  scroll_to_index: ^3.0.1
  flutter_html_all: ^3.0.0-alpha.6
  device_preview: ^1.2.0
  chunked_stream: ^1.4.2

  uri: ^1.0.0
  # jitsi_meet: 4.0.0 # previous stable version used was ^2.0.0
  jitsi_meet_flutter_sdk: ^10.3.0
  flutter_animator: ^3.2.2
  sprintf: ^7.0.0
  pretty_qr_code: ^3.3.0
  image_cropper: ^8.0.2
  screenshot: ^3.0.0
  device_calendar: ^4.3.3
  #  device_calendar: any
  animator: ^3.3.0
  rect_getter: ^1.1.0
  audioplayers: ^6.1.0
  animate_do: ^3.3.4
  flutter_countdown_timer: ^4.1.0
  text_scroll: ^0.2.0
  flutter_math_fork: ^0.7.2
  group_button: ^5.3.4

  lottie: ^3.1.3
  particles_fly: ^0.0.9
  flutter_launcher_icons: ^0.14.1
  flutter_native_splash: ^2.4.1
  webview_flutter: ^4.13.0
  # flutter_cashfree_pg_sdk: ^2.0.18+21
  flutter_cashfree_pg_sdk: ^2.2.3+41
  connectivity_plus: ^6.1.0
  flutter_bounceable: ^1.1.0
  flutter_rating_bar: ^4.0.1
  image_picker: ^1.1.2
  go_router: ^14.3.0
  flutter_callkit_incoming: ^2.5.1

  #Replaced Dependencies for null safety

  #toast: ^0.1.5
  fluttertoast: ^8.2.8

  #TODO
  #dynamic_theme: ^1.0.0
  dynamic_theme:
    git:
      url: https://github.com/Norbert515/dynamic_theme.git
      #url: git://github.com/Norbert515/dynamic_theme
  #dynamic_themes: ^0.1.0+1

  #google_map_location_picker: 3.3.3
  #place_picker: 0.9.12 #replacement of google_map_location_picker

  #getflutter: ^1.0.11
  #search_widget: ^1.0.2
  getwidget: ^5.0.0

  #TODO
  #flutter_swiper: ^1.1.6
  card_swiper: ^3.0.1

  #facebook_deeplinks: ^0.1.0
  #  flutter_facebook_sdk: ^1.0.0

  #sms_otp_auto_verify: ^1.2.2
  # android_sms_retriever: ^1.3.3  # REMOVED: Kotlin compatibility issue

  #flutter_country_picker: ^0.1.6
  country_code_picker: ^3.0.0

  #eventhandler: ^1.0.4
  eventify: ^1.0.1

  #progress_dialog: ^1.2.4
  #  ndialog: ^4.4.0
  # google_map_location_picker: 3.3.3
  # place_picker: 0.9.12 #replacement of google_map_location_picker

  # for ai bot
  sigv4: ^5.0.0
  uuid: ^4.5.1
  flutter_chat_ui: ^1.6.15
  amazon_cognito_identity_dart_2: ^3.6.5
  aws_lex_runtime_api: ^2.0.0
  syncfusion_flutter_calendar: ^27.1.53
  test: ^1.25.7
  permission_handler: ^11.3.1
  # device_info_plus: ^10.1.2

flutter_icons:
  android: true
  ios: true
  image_path: "assets/new_assets/images/oneapp_logo.png"
  adaptive_icon_foreground: "assets/new_assets/images/oneapp_logo.png"
  adaptive_icon_background: "#F44336"

  #Temporarily Removed Dependencies for null safety
  #flutter_dialogflow: ^0.1.2
  #super_qr_reader: ^2.0.2
  #sms: ^0.2.4
  #photo: 0.4.8
  #chewie: ^1.2.2
  #dependency_overrides:
  #  photo_manager: 0.6.0

  #Permanently Removed Dependencies for null safety
  #overlay_container: ^0.0.4+1
  #autocomplete_textfield: ^1.7.3
  #facebook_analytics_plugin: ^0.0.1+8
  #geocoder: ^0.2.1
  #flutter_open_whatsapp: ^0.1.2
  #smiley_rating_dialog: ^0.0.1
  #ticket_card: ^0.2.0
  #esys_flutter_share: ^1.0.2

# CAUTION TEMPORARY FIX: Phone Number Validation
#  - libphonenumber_plugin is a direct dependency of intl_phone_number_input lib which retrieves region info from phone number on Android and iOS.authors:
#  - intl_phone_number_input(v0.7.0) adds libphonenumber_plugin(v0.2.5) which makes use of PhoneNumberKit in iOS to retrieve region info
#  - ISSUE: libphonenumber_plugin(v0.2.5) through the use of PhoneNumberKit is unable to retrieve region info from phone number
#  - FIX: libphonenumber_plugin has been forcefully downgraded to version 0.2.3 using dependency overrides
#  - NOTE: This is a TEMPORARY FIX which will work for now but needs to be fixed permanently later
dependency_overrides:
  #  libphonenumber_plugin: 0.2.3
  intl: 0.19.0
  package_info_plus: ^8.3.0 #overrides for razorpay flutter - updated for Kotlin compatibility
  sprintf: ^7.0.0
  http: ^1.2.2
  share_plus: ^10.0.0

  # Override fluttertoast to use local stub implementation across all packages
  fluttertoast:
    path: ./fluttertoast_stub

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec


# The following section is specific to Flutter.
flutter:
  uses-material-design: true

  assets:
    - lang/en.json
    - lang/zh.json
    - assets/new_assets/theme/theme.json
    - assets/new_assets/images/
    - assets/images/
    - images/
    - assets/new_assets/audio/landline.caf
    - assets/new_assets/json/no_internet.json
    - assets/anim/electronic_fund_transfer.json
    - assets/anim/wired-lineal-999-money-check.json
    - assets/new_assets/json/scroll_explore.json
    - assets/new_assets/images/gateTop.png
    - assets/new_assets/images/payTop.png
    - assets/new_assets/images/oneapp_logo.png
    - assets/new_assets/images/meetTop.webp
    - assets/new_assets/images/socTop.png
    - assets/new_assets/images/gateBlur.jpg
    - assets/new_assets/images/meetBlur.jpg
    - assets/new_assets/images/under_main.png
    - assets/new_assets/images/socBlur.jpg
    - assets/new_assets/images/update_available.png
    - assets/new_assets/images/update.png
    - assets/user_consent_popup_content.json
    - assets/new_assets/json/gate_intro.json
    - assets/gate_intro.json
    - images/apartment/emergency/emergency_alert_failed.png
    - assets/gate222.json
    - assets/login.json
    - assets/meet.json
    - assets/NewAgent-81fafa8cda2c.json
    - assets/anim/no_network_connection.json
    - assets/orderOnline.json
    - assets/new_assets/json/walkThroughExplore.json
    - assets/anim/meet_done.json
    - assets/anim/success.json
    - assets/anim/payment_failed.json
    - assets/anim/anim_dashboard_loader.json
    - assets/anim/otpAnimation.json
    - assets/new_assets/json/confirmPassword.json
    - assets/new_assets/json/forgotPassword.json
    - assets/new_assets/json/newForgot.json
    - assets/new_assets/json/identifyAcc.json
    - assets/new_assets/json/updateNumber.json
    - assets/new_assets/audio/ping_ping.mp3
    - assets/new_assets/audio/ping.mp3
    - assets/addParticipents.json
    - assets/add_participent1.json
    - assets/new_assets/json/dashboard/dashboard_layout.json


    - images/
    - assets/images/
    - assets/images/
    - assets/images/recipe/
    - assets/cfg/
    - assets/img/
    - assets/user_consent_helper/
    - assets/user_consent_helper/images/
    - images/onepay_icons/
    - images/icons/
    - images/apartment/
    - images/intro/
    - assets/anim/hand_loader.json
    - assets/anim/oneapp_loader.json

    - assets/media/ads/ondc_five.jpg
    - assets/media/ads/ondc_four.jpg
    - assets/media/ads/ondc_one.jpg
    - assets/media/ads/ondc_three.jpg
    - assets/media/ads/ondc_two.jpg
    - assets/media/ads/ondc_six.jpg
    - images/onebook/
    - images/onegate/
    # - assets/media/ads/
    - assets/new_dashboard/images/homepage/
    - assets/home_ads.json
    - assets/addParticipents.json
    #    - images/onesociety
    - assets/new_dashboard/images/
    - assets/new_dashboard/images/onepay/viewall.png

    - images/default.png
    - images/groceries.jpg
    - images/grtwo.jpg
    - images/grthre.jpg
    - images/veg.jpg
    - images/complex.png
    #    - images/building.png
    - images/dummy_logo.png
    - images/floor.png
    - images/unit.png
    - images/pending-approval.png
    - images/no-connection.png
    - images/paymentfailed_icon.png
    - images/paymentsuccess_icon.png
    - images/403-icon.png
    - images/404-icon.png
    - images/500-icon.png
    - images/screen01.jpg
    - images/screen02.jpg
    - images/screen03.jpg

    - images/dash-bg.jpg
    - images/myflats.png
    - images/myvisitors.png
    - images/restaurants.png
    - images/homestylefood.png
    #    - images/default_restaurant.jpg
    #    - images/default_tiffin.jpg


    - images/dash1.png
    - images/dash2.png
    - images/dash3.png
    - images/dash4.png
    #
    #
    #
    #
    #
    #
    - images/bg.jpg
    #    - images/bg.png
    #
    - images/frozen.jpg
    - images/bev.jpg
    - images/brand_f.jpg
    - images/be.jpg
    - images/eggs.jpg
    - images/home.jpg
    - images/nonveg.jpg
    - images/apple.jpg
    - images/grapes.jpg
    - images/guava.jpg
    - images/kiwi.jpg
    - images/lemons.jpg
    - images/pineapple.jpg
    - images/tomato.jpg
    - images/gro.jpg
    - images/vesa.webp
    - images/chsone.webp
    - images/vizlog_icon.webp
    - images/restaurant_card.jpg
    - images/society_card.jpg
    - images/vizlog_card.jpg
    - images/tiffin_card.jpg
    - images/burger-king.png
    - images/mcd.jpg
    - images/avatar.jpg
    - images/in.png
    - images/out.png
    - images/building-bg.png
  #-/assets/media/ads/ondc_one.jpg


  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.


  # To add assets to your application, add an assets section, like this:
  # assets:
  #  - images/a_dot_burr.jpeg
  #  - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

  fonts:
    - family: FlutterIcon
      fonts:
        - asset: fonts/flutterIcon.ttf
    - family: Roboto-Light
      fonts:
        - asset: fonts/Roboto-Light.ttf
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
    - family: Roboto-Medium
      fonts:
        - asset: fonts/Roboto-Medium.ttf
    - family: Roboto-Bold
      fonts:
        - asset: fonts/Roboto-Bold.ttf

    - family: Gilroy-Regular
      fonts:
        - asset: fonts/Gilroy-Regular.otf
    - family: Gilroy-SemiBold
      fonts:
        - asset: fonts/Gilroy-SemiBold.otf
    - family: Gilroy-Bold
      fonts:
        - asset: fonts/Gilroy-Bold.otf

    - family: Gilroy
      fonts:
        - asset: fonts/Gilroy-Regular.otf
          weight: 300
        - asset: fonts/Gilroy-SemiBold.otf
          weight: 400
        - asset: fonts/Gilroy-Bold.otf
          weight: 700

    - family: Raleway
      fonts:
        - asset: fonts/Raleway-Regular.ttf
        - asset: fonts/Raleway-Bold.ttf
          weight: 700
        - asset: fonts/Raleway-Black.ttf
          weight: 900
    - family: RobotoCondensed
      fonts:
        - asset: fonts/RobotoCondensed-Regular.ttf
        - asset: fonts/RobotoCondensed-Bold.ttf
          weight: 700


    - family: Geist
      fonts:
        - asset: assets/new_assets/fonts/Geist-Regular.ttf
        - asset: assets/new_assets/fonts/Geist-Bold.ttf
          weight: 700
        - asset: assets/new_assets/fonts/Geist-Light.ttf
          weight: 300


flutter_intl:
  enabled: true

flutter_native_splash:
  color: "#ffffff"
  background_image: assets/new_assets/images/oneapp_logo_old.png
  color_dark: "#121212"
  background_image_dark: assets/new_assets/images/oneapp_logo_old.png
  image_ios: assets/new_assets/images/oneapp_logo_old.png



  android_12:
    color_dark: "#ffffff"
    color: "#ffffff"
    #  image: assets/new_assets/images/oneapp_logo_old.png
    #  background_image_dark: assets/new_assets/images/oneapp_logo_old.png


  web: false



# assets/new_assets/images/oneapp_logo.png