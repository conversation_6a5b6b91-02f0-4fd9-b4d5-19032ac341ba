import UIKit
import Flutter
import Firebase
import GoogleMaps
import FBSDKCoreKit
import AVFoundation
import AudioToolbox

//import GooglePlaces
@main
@objc class AppDelegate: FlutterAppDelegate {
  private var audioPlayer: AVAudioPlayer?

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    FirebaseApp.configure() // <-- Required for Firebase to work

    let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
    let sonicBrandingChannel = FlutterMethodChannel(name: "sonic_branding",
                                                   binaryMessenger: controller.binaryMessenger)

    sonicBrandingChannel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
      switch call.method {
      case "playPaymentSuccessSound":
        self?.playSonicBrandingSound(soundName: "payment_success_sonic", result: result)
      case "playCustomNotificationSound":
        self?.playSonicBrandingSound(soundName: "custom_notification", result: result)
      case "playLandlineSound":
        self?.playSonicBrandingSound(soundName: "landline", result: result)
      case "stopSound":
        self?.stopSonicBrandingSound(result: result)
      case "testSoundWithFallback":
        self?.testSoundWithFallback(result: result)
      default:
        result(FlutterMethodNotImplemented)
      }
    }

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // MARK: - Sonic Branding Methods

  private func playSonicBrandingSound(soundName: String, result: @escaping FlutterResult) {
    // Add 500ms delay as required for payment success
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
      self?.playAudioFile(soundName: soundName, result: result)
    }
  }

  private func playAudioFile(soundName: String, result: @escaping FlutterResult) {
    print("🔊 Attempting to play sound: \(soundName)")

    // Stop any currently playing sound
    audioPlayer?.stop()
    audioPlayer = nil

    // Try to load the sound from bundle
    guard let soundURL = Bundle.main.url(forResource: soundName, withExtension: "wav") ??
                         Bundle.main.url(forResource: soundName, withExtension: "mp3") else {
      print("❌ Sound file not found: \(soundName)")
      playFallbackSound(result: result)
      return
    }

    do {
      // Configure audio session
      try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
      try AVAudioSession.sharedInstance().setActive(true)

      // Create and configure audio player
      audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
      audioPlayer?.prepareToPlay()

      // Play the sound
      if audioPlayer?.play() == true {
        print("🔊 Sound started: \(soundName)")
        result(true)
      } else {
        print("❌ Failed to start sound: \(soundName)")
        playFallbackSound(result: result)
      }

    } catch {
      print("❌ Error playing sound: \(soundName), error: \(error)")
      playFallbackSound(result: result)
    }
  }

  private func playFallbackSound(result: @escaping FlutterResult) {
    print("🔄 Playing fallback sound")

    // Play system sound as fallback
    AudioServicesPlaySystemSound(1007) // System notification sound
    result(true)
  }

  private func stopSonicBrandingSound(result: @escaping FlutterResult) {
    audioPlayer?.stop()
    audioPlayer = nil
    print("🔇 Sound stopped")
    result(true)
  }

  private func testSoundWithFallback(result: @escaping FlutterResult) {
    print("🧪 Testing sound with fallback")
    playAudioFile(soundName: "nonexistent_sound", result: result)
  }
}
