<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.cubeone.app">

    <!-- App Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
    <uses-permission android:name="android.permission.PROJECT_MEDIA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- Intent Queries -->
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="http" />
        </intent>
    </queries>

    <application
        android:name=".Application"
        android:icon="@mipmap/ic_launcher"
        android:label="oneapp"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true"
        android:enableOnBackInvokedCallback="true"
        tools:replace="android:label">

        <!-- Firebase & Facebook Meta-data -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/oneapp_logo_notication" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/whats" />

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:supportsRtl="true"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize">

            <!-- Deeplinking for visitor approval -->
            <intent-filter>
                <action android:name="myVisitorApproval" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <!-- App link verification with validation -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Only allow specific hosts and schemes -->
                <data
                    android:host="collab.cubeone.in"
                    android:scheme="https" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <meta-data
                android:name="flutter_deeplinking_enabled"
                android:value="true" />
            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/launch_background" />
            <meta-data
                android:name="com.google.firebase.messaging.default_notification_channel_id"
                android:value="visitor_approval_channel" />
            <meta-data
                android:name="com.google.firebase.messaging.default_notification_channel"
                android:value="@string/default_notification_channel_id" />
        </activity>

        <!-- Secure internal activity -->
        <activity
            android:name=".RedirectingActivity"
            android:exported="false" />

        <!-- Facebook SDK Activities -->
        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:exported="false"
            android:label="CubeOne" />

        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Only allow specific scheme for Facebook login -->
                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>

        <!-- Firebase Messaging Service -->
        <service
            android:name=".MyFirebaseMessagingService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Additional Application Services and Receivers -->
        <service
            android:name=".MyFirebaseService"
            android:exported="false" />
        <service
            android:name="org.jitsi.meet.sdk.ConnectionService"
            android:exported="true"
            android:permission="android.permission.BIND_TELECOM_CONNECTION_SERVICE">
            <intent-filter>
                <action android:name="android.telecom.ConnectionService" />
            </intent-filter>
        </service>

        <activity
            android:name="node_service.ApprovalActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:showOnLockScreen="true"
            android:theme="@style/ApprovalActivityTheme" />
        <activity
            android:name="com.gunschu.jitsi_meet.JitsiMeetPluginActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat"
            tools:replace="android:theme" />
        <activity
            android:name=".modules.vizlog.emergency_alert.EmergencyAlertActivity"
            android:exported="false"
            android:label="@string/title_activity_emergency_alert"
            android:launchMode="singleInstance"
            android:theme="@style/EmergencyAlertActivityTheme" />

        <!-- Notification Channel Test Activity - Secured -->
        <activity
            android:name=".NotificationChannelTest"
            android:label="Notification Channel Test"
            android:exported="false"
            android:theme="@style/Theme.AppCompat">
            <!-- Removed intent-filter to prevent external access -->
        </activity>

        <service
            android:name=".modules.vizlog.intercom.IntercomContactsService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.mythichelm.localnotifications.services.LocalNotificationsService"
            android:exported="false" />
        <service
            android:name="node_service.NodeService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="node_service.NotificationIntentService"
            android:exported="false" />
        <service
            android:name=".modules.vizlog.emergency_alert.EmergencyAlertService"
            android:enabled="true"
            android:exported="false" />

        <receiver
            android:name=".modules.vizlog.emergency_alert.EmergencyAlertReceiver"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name=".ApprovalReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="node_service.NotifyMemberApproval.MEMBER_APPROVAL_REQUEST" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="node_service.ApprovalReceiver"
            android:exported="false" />

        <provider
            android:name="vn.hunghd.flutterdownloader.DownloadedFileProvider"
            android:authorities="com.cubeone.app.flutter_downloader.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <receiver
            android:name="com.appsflyer.SingleInstallBroadcastReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.cubeone.app.com.shekarmudaliyar.social_share"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider>
        <receiver
            android:name=".CallActionReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="ACCEPT_CALL" />
                <action android:name="REJECT_CALL" />
                <action android:name="LEAVE_AT_GATE" />
            </intent-filter>
        </receiver>
    </application>
</manifest>
