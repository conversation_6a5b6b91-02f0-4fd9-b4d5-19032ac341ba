package com.cubeone.app

import android.app.NotificationManager
import android.content.*
import android.content.pm.PackageManager
import android.media.MediaPlayer
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import androidx.annotation.NonNull
import com.cubeone.app.security.IntentSecurityManager
import com.cubeone.app.security.IntentValidationUtils
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodChannel
import com.cubeone.app.modules.vizlog.intercom.IntercomConstants

class MainActivity : FlutterFragmentActivity() {

    private var mMethodChannel: MethodChannel? = null
    private var mIntercomMessagingEventChannel: EventChannel? = null
    private var mIntercomMessagingEventEmitter: EventChannel.EventSink? = null
    private val notificationQueue = mutableListOf<Map<String, Any?>>()
    private var mediaPlayer: MediaPlayer? = null

    private var push_notification_redirect: String? = null

    companion object {
        const val CHANNEL = "com.cubeone.app/MainActivity"
        const val EVENT_CHANNEL_INTERCOM_MESSAGING = "intercom_messaging_event_channel"
        const val NOTIFICATION_SOUND_CHANNEL = "notification_sound_channel"
    }

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        Log.e("MainActivity", "FlutterEngine configured")

        mMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        callListner()

        initIntercomMessagingEventChannel(flutterEngine)

        // Debug Check Channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "com.cubeone.app/debugCheck")
            .setMethodCallHandler { call, result ->
                if (call.method == "isDebuggingEnabled") {
                    val isDebuggingEnabled = try {
                        Settings.Secure.getInt(contentResolver, Settings.Global.ADB_ENABLED, 0) == 1
                    } catch (e: SecurityException) {
                        Log.e("DebugCheck", "Permission denied for ADB status", e)
                        result.error("1", "Permission denied", null)
                        return@setMethodCallHandler
                    }
                    result.success(isDebuggingEnabled)
                } else {
                    result.notImplemented()
                }
            }

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, NOTIFICATION_SOUND_CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "playSound" -> {
                        val soundName = call.argument<String>("soundName")
                        playSound(soundName)
                        result.success(null)
                    }
                    "stopSound" -> {
                        stopSound()
                        result.success(null)
                    }
                    else -> {
                        result.notImplemented()
                    }
                }
            }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.e("MainActivity", "onCreate called")

        val bundle: Bundle? = intent.extras
        bundle?.let {
            val dataMap = mutableMapOf<String, Any?>()
            for (key in it.keySet()) {
                val value = it.get(key)
                Log.e("onCreate", "$key : ${value ?: "NULL"}")
                dataMap[key] = value
            }
            queueNotification(dataMap)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)

        // SECURITY FIX: Block any intent that might cause redirection
        if (IntentSecurityManager.blockRedirectionIntent(intent)) {
            Log.i("MainActivity", "Intent passed security check")
        } else {
            Log.e("MainActivity", "Intent blocked by security manager - potential redirection risk")
            return
        }

        // Additional validation using existing methods
        if (!validateIntent(intent)) {
            Log.e("MainActivity", "Intent validation failed, ignoring intent")
            return
        }

        val bundle: Bundle? = intent.extras
        bundle?.let {
            // Create a safe map with only whitelisted keys
            val safeData = mutableMapOf<String, Any?>()

            // Define allowed keys
            val allowedKeys = setOf("click_action", "title", "body", "visitor_name", "visitor_id", "status")

            for (key in it.keySet()) {
                Log.e("onNewIntent", "$key : ${it.get(key) ?: "NULL"}")

                // Only process whitelisted keys
                if (allowedKeys.contains(key)) {
                    val value = it.get(key)

                    // Validate string values
                    if (value is String && !containsUnsafeContent(value)) {
                        safeData[key] = value

                        // Special handling for click_action
                        if (key == "click_action" && value == "myVisitorApproval") {
                            // SECURITY FIX: Only send minimal data to Flutter
                            sendDataToFlutter(mapOf("click_action" to value))
                        }
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        registerIntercomMessagingReceiver()
    }

    private fun queueNotification(dataMap: Map<String, Any?>) {
        // Create a safe copy with only allowed keys and validated values
        val safeDataMap = mutableMapOf<String, Any?>()
        val allowedKeys = setOf("click_action", "title", "body", "visitor_name", "visitor_id", "status")

        for ((key, value) in dataMap) {
            if (allowedKeys.contains(key) && value is String && !containsUnsafeContent(value)) {
                safeDataMap[key] = value
            }
        }

        // Only add to queue if there are safe values
        if (safeDataMap.isNotEmpty()) {
            notificationQueue.add(safeDataMap)
        }
    }

    private fun sendDataToFlutter(dataMap: Map<String, Any?>) {
        // Create a safe copy with only allowed keys and validated values
        val safeDataMap = mutableMapOf<String, Any?>()
        val allowedKeys = setOf("click_action", "title", "body", "visitor_name", "visitor_id", "status")

        // SECURITY FIX: Add additional logging for security auditing
        Log.d("MainActivity", "Preparing to send data to Flutter with keys: ${dataMap.keys}")

        for ((key, value) in dataMap) {
            // Only allow whitelisted keys
            if (allowedKeys.contains(key)) {
                if (value is String) {
                    // Validate string values to prevent injection
                    if (!containsUnsafeContent(value)) {
                        // SECURITY FIX: Additional check for URL-like content
                        if (!value.contains("://") && !value.startsWith("http") && !value.startsWith("intent:")) {
                            safeDataMap[key] = value
                        } else {
                            Log.w("MainActivity", "Blocked URL-like value for key: $key")
                        }
                    } else {
                        Log.w("MainActivity", "Blocked unsafe content for key: $key")
                    }
                } else if (value != null) {
                    // Non-string values are considered safe
                    safeDataMap[key] = value
                }
            } else {
                Log.w("MainActivity", "Skipped non-whitelisted key: $key")
            }
        }

        // Only send if there are safe values
        if (safeDataMap.isNotEmpty()) {
            flutterEngine?.let { engine ->
                Log.d("MainActivity", "Sending safe data to Flutter: $safeDataMap")
                MethodChannel(engine.dartExecutor.binaryMessenger, CHANNEL)
                    .invokeMethod("handleNotification", safeDataMap)
            } ?: Log.e("MainActivity", "FlutterEngine is null, cannot send data.")
        } else {
            Log.w("MainActivity", "No safe data to send to Flutter")
        }
    }

    private fun initIntercomMessagingEventChannel(flutterEngine: FlutterEngine) {
        mIntercomMessagingEventChannel = EventChannel(flutterEngine.dartExecutor.binaryMessenger, EVENT_CHANNEL_INTERCOM_MESSAGING)
        mIntercomMessagingEventChannel?.setStreamHandler(
            object : EventChannel.StreamHandler {
                override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                    mIntercomMessagingEventEmitter = events
                }

                override fun onCancel(arguments: Any?) {
                    mIntercomMessagingEventEmitter = null
                }
            }
        )
    }

    private fun playSound(soundName: String?) {
        soundName?.let { name ->
            Log.d("SonicBranding", "🔊 Attempting to play sound: $name")
            val resourceId = resources.getIdentifier(name, "raw", packageName)
            if (resourceId != 0) {
                try {
                    // Stop any currently playing sound
                    mediaPlayer?.let { mp ->
                        try {
                            if (mp.isPlaying) {
                                mp.stop()
                            }
                        } catch (e: Exception) {
                            Log.w("SonicBranding", "Error stopping previous sound", e)
                        }
                        mp.release()
                    }

                    mediaPlayer = MediaPlayer.create(this, resourceId)
                    mediaPlayer?.let { mp ->
                        mp.setOnCompletionListener { player ->
                            Log.d("SonicBranding", "🔊 Sound playback completed: $name")
                            player.release()
                        }
                        mp.setOnErrorListener { player, what, extra ->
                            Log.e("SonicBranding", "⚠️ Error playing sound: $name, what: $what, extra: $extra")
                            player.release()
                            true
                        }

                        // Start playback
                        mp.start()
                        Log.d("SonicBranding", "🔊 Sound started: $name")
                    }
                } catch (e: Exception) {
                    Log.e("SonicBranding", "⚠️ Exception playing sound: $name", e)
                }
            } else {
                Log.e("SonicBranding", "⚠️ Sound resource not found: $name")
            }
        }
    }

    private fun stopSound() {
        mediaPlayer?.let { mp ->
            try {
                if (mp.isPlaying) {
                    mp.stop()
                }
            } catch (e: Exception) {
                Log.w("SonicBranding", "Error stopping sound", e)
            }
            mp.release()
        }
        mediaPlayer = null
        Log.d("SonicBranding", "🔇 Sound stopped")
    }

    private fun callListner() {
        mMethodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "node_url" -> {
                    val sharedPref: SharedPreferences = getSharedPreferences("preferences", Context.MODE_PRIVATE)
                    val args = call.arguments as HashMap<String, String>
                    sharedPref.edit().putString("node_url_key", args["node_url_key"]).apply()
                    result.success(null)
                }

                "requestScreenOverDrawPermission" -> {
                    requestScreenOverDrawPermission()
                    result.success(null)
                }

                "hasScreenOverDrawPermission" -> {
                    val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        Settings.canDrawOverlays(this)
                    } else {
                        true
                    }
                    result.success(hasPermission)
                }

                "hasChangeDndSettingPermission" -> {
                    val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                        notificationManager.isNotificationPolicyAccessGranted
                    } else {
                        true
                    }
                    result.success(hasPermission)
                }

                "requestChangeDndSettingPermission" -> {
                    requestChangeDndPermission()
                    result.success(null)
                }

                else -> result.notImplemented()
            }
        }
    }

    private fun requestScreenOverDrawPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            startActivity(Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION))
        }
    }

    private fun requestChangeDndPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_NOTIFICATION_POLICY_ACCESS_SETTINGS)
            startActivity(intent)
        }
    }

    private fun registerIntercomMessagingReceiver() {
        val intentFilter = IntentFilter(IntercomConstants.ACTION_INTERCOM_RECEIVE_MESSAGE)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(mIntercomMessagingReceiver, intentFilter, Context.RECEIVER_EXPORTED)
        } else {
            registerReceiver(mIntercomMessagingReceiver, intentFilter)
        }
    }

    private val mIntercomMessagingReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val message = intent.getStringExtra("message")
            mIntercomMessagingEventEmitter?.success(mapOf("message" to message))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(mIntercomMessagingReceiver)
    }

    /**
     * Validates an intent to prevent intent redirection vulnerabilities
     *
     * @param intent The intent to validate
     * @return true if the intent is valid, false otherwise
     */
    private fun validateIntent(intent: Intent): Boolean {
        // Use our centralized validation utility for consistency
        return IntentValidationUtils.isValidIntent(intent)
    }

    /**
     * Checks if a string contains potentially unsafe content
     *
     * @param value The string to check
     * @return true if the string contains unsafe content, false otherwise
     */
    private fun containsUnsafeContent(value: String): Boolean {
        // Use our centralized validation utility for consistency
        return IntentValidationUtils.containsUnsafeContent(value)
    }
}