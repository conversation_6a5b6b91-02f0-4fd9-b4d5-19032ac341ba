import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/enhanced_transaction_history.dart';

void main() {
  group('Enhanced Transaction History Tests', () {
    testWidgets('Should display radio buttons for search type selection', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EnhancedTransactionHistoryScreen(),
        ),
      );

      // Verify radio buttons are present
      expect(find.text('Mobile & Date'), findsOneWidget);
      expect(find.text('Transaction ID'), findsOneWidget);
      
      // Verify default selection is mobile_date
      final mobileRadio = find.byType(RadioListTile<String>).first;
      final RadioListTile<String> mobileRadioWidget = tester.widget(mobileRadio);
      expect(mobileRadioWidget.groupValue, equals('mobile_date'));
    });

    testWidgets('Should show mobile and date range fields when mobile_date is selected', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EnhancedTransactionHistoryScreen(),
        ),
      );

      // Verify mobile and date fields are visible
      expect(find.text('Mobile Number *'), findsOneWidget);
      expect(find.text('From Date *'), findsOneWidget);
      expect(find.text('To Date *'), findsOneWidget);
      
      // Verify transaction ID field is not visible
      expect(find.text('Transaction Reference ID *'), findsNothing);
    });

    testWidgets('Should show transaction ID field when transaction_ref is selected', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EnhancedTransactionHistoryScreen(),
        ),
      );

      // Tap on Transaction ID radio button
      await tester.tap(find.text('Transaction ID'));
      await tester.pumpAndSettle();

      // Verify transaction ID field is visible
      expect(find.text('Transaction Reference ID *'), findsOneWidget);
      
      // Verify mobile and date fields are not visible
      expect(find.text('Mobile Number *'), findsNothing);
      expect(find.text('From Date *'), findsNothing);
      expect(find.text('To Date *'), findsNothing);
    });

    testWidgets('Should validate mobile number input', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EnhancedTransactionHistoryScreen(),
        ),
      );

      // Find mobile number field and enter invalid input
      final mobileField = find.widgetWithText(TextFormField, 'Mobile Number *');
      await tester.enterText(mobileField, '123');
      
      // Tap search button to trigger validation
      await tester.tap(find.text('Search Transactions'));
      await tester.pumpAndSettle();

      // Verify validation error appears
      expect(find.text('Please enter valid 10-digit mobile number'), findsOneWidget);
    });

    testWidgets('Should validate transaction reference ID input', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EnhancedTransactionHistoryScreen(),
        ),
      );

      // Switch to transaction ID search
      await tester.tap(find.text('Transaction ID'));
      await tester.pumpAndSettle();

      // Enter short transaction ID
      final transactionField = find.widgetWithText(TextFormField, 'Transaction Reference ID *');
      await tester.enterText(transactionField, '123');
      
      // Tap search button to trigger validation
      await tester.tap(find.text('Search Transactions'));
      await tester.pumpAndSettle();

      // Verify validation error appears
      expect(find.text('Transaction ID must be at least 6 characters'), findsOneWidget);
    });

    testWidgets('Should show loading state during search', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EnhancedTransactionHistoryScreen(),
        ),
      );

      // Enter valid mobile number
      final mobileField = find.widgetWithText(TextFormField, 'Mobile Number *');
      await tester.enterText(mobileField, '9876543210');

      // Select dates (simulate by setting text directly)
      final fromDateField = find.widgetWithText(TextFormField, 'From Date *');
      await tester.enterText(fromDateField, '01/12/2024');
      
      final toDateField = find.widgetWithText(TextFormField, 'To Date *');
      await tester.enterText(toDateField, '31/12/2024');

      // Tap search button
      await tester.tap(find.text('Search Transactions'));
      await tester.pump();

      // Verify loading state
      expect(find.text('Searching...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsWidgets);
    });

    testWidgets('Should display search results after successful search', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EnhancedTransactionHistoryScreen(),
        ),
      );

      // Enter valid mobile number that will return results
      final mobileField = find.widgetWithText(TextFormField, 'Mobile Number *');
      await tester.enterText(mobileField, '9876543210');

      // Select dates
      final fromDateField = find.widgetWithText(TextFormField, 'From Date *');
      await tester.enterText(fromDateField, '01/12/2024');
      
      final toDateField = find.widgetWithText(TextFormField, 'To Date *');
      await tester.enterText(toDateField, '31/12/2024');

      // Tap search button and wait for results
      await tester.tap(find.text('Search Transactions'));
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Verify results are displayed
      expect(find.textContaining('Transaction Results'), findsOneWidget);
      expect(find.text('Mobile Prepaid'), findsOneWidget);
      expect(find.text('Electricity Bill'), findsOneWidget);
    });

    testWidgets('Should display no results message when no transactions found', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EnhancedTransactionHistoryScreen(),
        ),
      );

      // Enter mobile number that will return no results
      final mobileField = find.widgetWithText(TextFormField, 'Mobile Number *');
      await tester.enterText(mobileField, '1234567890');

      // Select dates
      final fromDateField = find.widgetWithText(TextFormField, 'From Date *');
      await tester.enterText(fromDateField, '01/12/2024');
      
      final toDateField = find.widgetWithText(TextFormField, 'To Date *');
      await tester.enterText(toDateField, '31/12/2024');

      // Tap search button and wait for results
      await tester.tap(find.text('Search Transactions'));
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Verify no results message is displayed
      expect(find.text('No transactions found'), findsOneWidget);
      expect(find.text('Try adjusting your search criteria'), findsOneWidget);
    });

    testWidgets('Should clear form fields when switching search types', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EnhancedTransactionHistoryScreen(),
        ),
      );

      // Enter mobile number
      final mobileField = find.widgetWithText(TextFormField, 'Mobile Number *');
      await tester.enterText(mobileField, '9876543210');

      // Switch to transaction ID search
      await tester.tap(find.text('Transaction ID'));
      await tester.pumpAndSettle();

      // Switch back to mobile & date search
      await tester.tap(find.text('Mobile & Date'));
      await tester.pumpAndSettle();

      // Verify mobile field is cleared
      final clearedMobileField = find.widgetWithText(TextFormField, 'Mobile Number *');
      final TextFormField mobileWidget = tester.widget(clearedMobileField);
      expect(mobileWidget.controller?.text, isEmpty);
    });
  });
}
