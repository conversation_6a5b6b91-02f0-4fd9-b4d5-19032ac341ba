import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sso_futurescape/custom_widgets/b_assured_logo_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';
import 'package:sso_futurescape/services/sonic_branding_service.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/complaint_registration.dart';
import 'package:sso_futurescape/ui/module/sso/utility_bill_payment/enhanced_transaction_history.dart';

void main() {
  group('Bharat Connect MobiKwik Integration Tests', () {
    testWidgets('B Assured Logo Widget Test', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BAssuredLogo(height: 40),
          ),
        ),
      );

      // Verify the widget is rendered
      expect(find.byType(BAssuredLogo), findsOneWidget);

      // Verify the logo has correct height
      final logoWidget = tester.widget<BAssuredLogo>(find.byType(BAssuredLogo));
      expect(logoWidget.height, equals(40));

      // Verify either SVG or placeholder is present
      final hasSvg = find.byType(SvgPicture).evaluate().isNotEmpty;
      final hasText = find.text('B\nAssured').evaluate().isNotEmpty;
      expect(hasSvg || hasText, isTrue,
          reason: 'Either SVG logo or placeholder text should be present');
    });

    testWidgets('Secured By BBPS Widget Test', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SecuredByBBPS(),
          ),
        ),
      );

      // Verify the widget is rendered
      expect(find.byType(SecuredByBBPS), findsOneWidget);
    });

    test('Sonic Branding Service Test', () async {
      // Test payment success sound
      final result1 = await SonicBrandingService.playPaymentSuccessSound();
      expect(result1, isA<bool>());

      // Test custom notification sound
      final result2 = await SonicBrandingService.playCustomNotificationSound();
      expect(result2, isA<bool>());

      // Test landline sound
      final result3 = await SonicBrandingService.playLandlineSound();
      expect(result3, isA<bool>());

      // Test stop sound
      final result4 = await SonicBrandingService.stopSound();
      expect(result4, isA<bool>());

      // Test sound with fallback
      final result5 = await SonicBrandingService.testSoundWithFallback();
      expect(result5, isA<bool>());
    });

    group('Logo Integration Tests', () {
      testWidgets('Bharat Connect Logo Transparency Test',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Container(
                decoration: BoxDecoration(
                  color: Colors.red, // Background to test transparency
                ),
                child: BAssuredLogo(height: 40),
              ),
            ),
          ),
        );

        // Verify the logo widget is rendered
        expect(find.byType(BAssuredLogo), findsOneWidget);
      });

      testWidgets('BBPS Logo Integration Test', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: SecuredByBBPS(),
            ),
          ),
        );

        // Verify the BBPS widget is rendered
        expect(find.byType(SecuredByBBPS), findsOneWidget);
      });
    });

    group('Audio Integration Tests', () {
      test('Payment Success Audio with Delay Test', () async {
        final stopwatch = Stopwatch()..start();

        await SonicBrandingService.playPaymentSuccessSound();

        stopwatch.stop();

        // Verify the 500ms delay is implemented
        expect(stopwatch.elapsedMilliseconds, greaterThanOrEqualTo(500));
      });

      test('Audio Service Error Handling Test', () async {
        // Test that methods don't throw exceptions
        expect(() async => await SonicBrandingService.playPaymentSuccessSound(),
            returnsNormally);
        expect(() async => await SonicBrandingService.stopSound(),
            returnsNormally);
      });
    });

    group('Screen Integration Tests', () {
      testWidgets('Complaint Registration Screen Test',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: ComplaintRegistrationScreen(),
          ),
        );

        // Verify the screen is rendered
        expect(find.byType(ComplaintRegistrationScreen), findsOneWidget);

        // Verify B Assured logo is present in AppBar
        expect(find.byType(BAssuredLogo), findsOneWidget);

        // Verify BBPS logo is present
        expect(find.byType(SecuredByBBPS), findsOneWidget);

        // Verify dual search radio buttons
        expect(find.text('Search via Mobile Number and Date'), findsOneWidget);
        expect(
            find.text('Search via Transaction Reference ID'), findsOneWidget);

        // Verify form fields are present
        expect(find.byType(TextFormField), findsAtLeast(2));

        // Verify submit button
        expect(find.text('Submit Complaint'), findsOneWidget);
      });

      testWidgets('Enhanced Transaction History Screen Test',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: EnhancedTransactionHistoryScreen(),
          ),
        );

        // Verify the screen is rendered
        expect(find.byType(EnhancedTransactionHistoryScreen), findsOneWidget);

        // Verify B Assured logo is present in AppBar
        expect(find.byType(BAssuredLogo), findsOneWidget);

        // Verify BBPS logo is present
        expect(find.byType(SecuredByBBPS), findsOneWidget);

        // Verify dual search radio buttons
        expect(find.text('Search via Mobile Number and Date'), findsOneWidget);
        expect(
            find.text('Search via Transaction Reference ID'), findsOneWidget);

        // Verify form fields are present
        expect(find.byType(TextFormField), findsAtLeast(2));

        // Verify search button
        expect(find.text('Search Transactions'), findsOneWidget);
      });

      testWidgets('Form Validation Test - Complaint Registration',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: ComplaintRegistrationScreen(),
          ),
        );

        // Try to submit without filling form
        await tester.tap(find.text('Submit Complaint'));
        await tester.pump();

        // Verify validation messages appear
        expect(find.text('Please enter mobile number'), findsOneWidget);
      });

      testWidgets('Form Validation Test - Transaction History',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: EnhancedTransactionHistoryScreen(),
          ),
        );

        // Try to search without filling form
        await tester.tap(find.text('Search Transactions'));
        await tester.pump();

        // Verify validation messages appear
        expect(find.text('Please enter mobile number'), findsOneWidget);
      });
    });
  });
}
