import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sso_futurescape/custom_widgets/b_assured_logo_widget.dart';
import 'package:sso_futurescape/custom_widgets/secured_by_bbps_widget.dart';
import 'package:sso_futurescape/services/sonic_branding_service.dart';

void main() {
  group('Bharat Connect MobiKwik Integration Tests', () {
    testWidgets('B Assured Logo Widget Test', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BAssuredLogo(height: 40),
          ),
        ),
      );

      // Verify the widget is rendered
      expect(find.byType(BAssuredLogo), findsOneWidget);

      // Verify the container structure
      expect(find.byType(Container), findsOneWidget);
    });

    testWidgets('Secured By BBPS Widget Test', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SecuredByBBPS(),
          ),
        ),
      );

      // Verify the widget is rendered
      expect(find.byType(SecuredByBBPS), findsOneWidget);
    });

    test('Sonic Branding Service Test', () async {
      // Test payment success sound
      final result1 = await SonicBrandingService.playPaymentSuccessSound();
      expect(result1, isA<bool>());

      // Test custom notification sound
      final result2 = await SonicBrandingService.playCustomNotificationSound();
      expect(result2, isA<bool>());

      // Test landline sound
      final result3 = await SonicBrandingService.playLandlineSound();
      expect(result3, isA<bool>());

      // Test stop sound
      final result4 = await SonicBrandingService.stopSound();
      expect(result4, isA<bool>());

      // Test sound with fallback
      final result5 = await SonicBrandingService.testSoundWithFallback();
      expect(result5, isA<bool>());
    });

    group('Logo Integration Tests', () {
      testWidgets('Bharat Connect Logo Transparency Test',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Container(
                decoration: BoxDecoration(
                  color: Colors.red, // Background to test transparency
                ),
                child: BAssuredLogo(height: 40),
              ),
            ),
          ),
        );

        // Verify the logo widget is rendered
        expect(find.byType(BAssuredLogo), findsOneWidget);
      });

      testWidgets('BBPS Logo Integration Test', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: SecuredByBBPS(),
            ),
          ),
        );

        // Verify the BBPS widget is rendered
        expect(find.byType(SecuredByBBPS), findsOneWidget);
      });
    });

    group('Audio Integration Tests', () {
      test('Payment Success Audio with Delay Test', () async {
        final stopwatch = Stopwatch()..start();

        await SonicBrandingService.playPaymentSuccessSound();

        stopwatch.stop();

        // Verify the 500ms delay is implemented
        expect(stopwatch.elapsedMilliseconds, greaterThanOrEqualTo(500));
      });

      test('Audio Service Error Handling Test', () async {
        // Test that methods don't throw exceptions
        expect(() async => await SonicBrandingService.playPaymentSuccessSound(),
            returnsNormally);
        expect(() async => await SonicBrandingService.stopSound(),
            returnsNormally);
      });
    });
  });
}
